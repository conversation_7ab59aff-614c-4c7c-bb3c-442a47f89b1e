#!/usr/bin/env python3
# simulate_live_trading.py
"""
Simulácia live_trading.py logiky s historickými dátami.
Umož<PERSON>u<PERSON> otestovať live trading logiku bez skutočného live tradingu.
"""
from __future__ import annotations
import os, sys, time, json, logging, argparse
from pathlib import Path
from datetime import datetime, timedelta, timezone
from typing import Dict, Any

import numpy as np
import pandas as pd
import torch

# Import our modules
from config_loader import ConfigLoader
from fallback_data_provider import FallbackDataProvider

try:
    from binance.client import Client as BinanceClient
    BINANCE_AVAILABLE = True
except ImportError:
    BINANCE_AVAILABLE = False
    BinanceClient = None

from stable_baselines3 import SAC
from stable_baselines3.common.vec_env import VecNormalize, DummyVecEnv

from scalping_env import ScalpingEnv
from indicators import calculate_and_merge_indicators
from trade_executor import TradeExecutor
from data_provider import DataProvider
from popart_sac import PopArtSAC
from agent import SimpleCNN1D, SafeReplayBuffer

# ──────────────────────────── LOGGING ─────────────────────────────────────
logging.basicConfig(
    level   = logging.INFO,
    format  = "%(asctime)s [%(name)s] [%(levelname)s] %(message)s",
    datefmt = "%Y-%m-%d %H:%M:%S",
    handlers=[logging.StreamHandler(sys.stdout)]
)
log = logging.getLogger("SimulateLiveTrading")

DEFAULT_CONFIG_PATH = Path("strategyConfig_scalp_1s.json")

def load_config(path: Path) -> Dict[str, Any]:
    """Load configuration from JSON/YAML file with environment variable substitution"""
    import yaml
    import os
    import re
    
    log.info(f"Loading config from: {path}")
    try:
        with open(path, 'r') as f:
            if path.suffix.lower() == '.json':
                content = f.read()
                # Replace environment variables
                def replace_env_var(match):
                    var_name = match.group(1)
                    return os.environ.get(var_name, match.group(0))
                content = re.sub(r'\$\{([^}]+)\}', replace_env_var, content)
                cfg = json.loads(content)
            elif path.suffix.lower() in ['.yaml', '.yml']:
                cfg = yaml.safe_load(f)
            else:
                raise ValueError(f"Unsupported config format: {path.suffix}")
    except Exception as e:
        log.error(f"Error loading/processing config {path}: {e}")
        raise
    
    # Debug API key loading
    coinapi_key = cfg.get("coinapi", {}).get("apiKey", "")
    if coinapi_key and not coinapi_key.startswith("${"):
        masked_key = coinapi_key[:8] + "..." + coinapi_key[-4:] if len(coinapi_key) > 12 else "***"
        log.info(f"🔍 DEBUG: Using CoinAPI key: {masked_key}")
    
    symbol = cfg.get("symbol") or cfg.get("dataProvider", {}).get("symbol", "UNKNOWN")
    log.info(f"Config loaded for symbol: {symbol}")
    return cfg

def load_historical_data(symbol: str, start_date: str, end_date: str, timeframe: str = "1s") -> pd.DataFrame:
    """Load historical data from parquet files"""
    log.info(f"Loading historical data for {symbol} from {start_date} to {end_date}")
    
    # Convert dates
    start_dt = pd.to_datetime(start_date).tz_localize('UTC')
    end_dt = pd.to_datetime(end_date).tz_localize('UTC') + pd.Timedelta(days=1) - pd.Timedelta(seconds=1)
    
    # Load data from parquet files
    parquet_dir = Path(f"parquet_processed/{symbol}/{timeframe}")
    if not parquet_dir.exists():
        raise FileNotFoundError(f"Parquet directory not found: {parquet_dir}")
    
    # Find all parquet files in date range
    all_data = []
    current_date = start_dt.date()
    end_date_obj = end_dt.date()
    
    while current_date <= end_date_obj:
        parquet_file = parquet_dir / f"{current_date}.parquet"
        if parquet_file.exists():
            log.info(f"Loading: {parquet_file}")
            df = pd.read_parquet(parquet_file)

            # Check if timestamp is in column (processed parquet format)
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.set_index('timestamp')

            # Ensure index is datetime
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)

            # Ensure timezone aware
            if df.index.tz is None:
                df.index = df.index.tz_localize('UTC')
            else:
                df.index = df.index.tz_convert('UTC')

            # Filter by time range
            df = df[(df.index >= start_dt) & (df.index <= end_dt)]
            if not df.empty:
                all_data.append(df)
        current_date += timedelta(days=1)
    
    if not all_data:
        raise ValueError(f"No data found for {symbol} in date range {start_date} to {end_date}")
    
    # Combine all data
    combined_df = pd.concat(all_data, axis=0).sort_index()
    log.info(f"Loaded {len(combined_df)} rows from {combined_df.index.min()} to {combined_df.index.max()}")
    
    return combined_df

class MockBinanceClient:
    """Mock Binance client for simulation"""
    def __init__(self):
        self.orders = []
        self.order_id = 1000
        
    def create_order(self, **kwargs):
        """Mock order creation"""
        order = {
            'orderId': self.order_id,
            'symbol': kwargs.get('symbol'),
            'side': kwargs.get('side'),
            'type': kwargs.get('type'),
            'quantity': kwargs.get('quantity'),
            'price': kwargs.get('price'),
            'status': 'FILLED',
            'executedQty': kwargs.get('quantity'),
            'fills': [{
                'price': kwargs.get('price'),
                'qty': kwargs.get('quantity'),
                'commission': '0',
                'commissionAsset': 'USDC'
            }]
        }
        self.orders.append(order)
        self.order_id += 1
        return order
    
    def cancel_order(self, **kwargs):
        """Mock order cancellation"""
        return {'orderId': kwargs.get('orderId'), 'status': 'CANCELED'}
    
    def get_order(self, **kwargs):
        """Mock get order"""
        order_id = kwargs.get('orderId')
        for order in self.orders:
            if order['orderId'] == order_id:
                return order
        return None

def simulate_live_trading_logic(cfg: Dict[str, Any], historical_data: pd.DataFrame, 
                               start_date: str, end_date: str, 
                               out_trades: str = None, out_equity: str = None) -> Dict[str, Any]:
    """Simulate live trading logic with historical data"""
    
    log.info("🚀 Starting live trading simulation...")
    
    # Setup like in live_trading.py
    symbol_cfg = cfg.get("symbol") or cfg.get("dataProvider", {}).get("symbol", "XRPUSDC")
    timeframe = cfg["primaryTimeframe"]
    use_1s_decisions = cfg.get("use_1s_decisions", False)
    
    log.info(f"Symbol: {symbol_cfg}")
    log.info(f"Timeframe: {timeframe}")
    log.info(f"Use 1s decisions: {use_1s_decisions}")
    
    # Load model (exactly like live_trading.py)
    model_files = list(Path(".").glob("sac_*_steps.zip"))
    if not model_files:
        raise FileNotFoundError("No SAC model files found!")
    
    latest_model = sorted(model_files, key=lambda x: int(x.stem.split('_')[1]))[-1]
    log.info(f"Using model: {latest_model}")
    
    # Load VecNormalize (try both naming patterns)
    vecnorm_files = list(Path(".").glob("vecnorm_*.pkl")) + list(Path(".").glob("sac_*.vecnorm.pkl"))
    if not vecnorm_files:
        raise FileNotFoundError("No VecNormalize files found!")

    # Sort by step number (handle both naming patterns)
    def extract_steps(path):
        if "vecnorm_" in path.name:
            return int(path.stem.split('_')[1])
        else:  # sac_*.vecnorm.pkl format
            # Extract number from sac_XXXXXX.vecnorm.pkl
            parts = path.stem.split('.')
            return int(parts[0].split('_')[1])

    latest_vecnorm = sorted(vecnorm_files, key=extract_steps)[-1]
    log.info(f"Using vecnorm: {latest_vecnorm}")
    
    # Calculate expected observation space size like simulate_trading_new.py
    feature_cols = cfg["envSettings"]["feature_columns"]
    lookback = cfg["envSettings"].get("state_lookback", 30)
    expected_obs_size = len(feature_cols) * lookback + 11
    
    log.info(f"🔧 Environment setup:")
    log.info(f"   Feature columns: {len(feature_cols)} features")
    log.info(f"   Lookback: {lookback}")
    log.info(f"   Expected obs size: {expected_obs_size}")
    
    # Setup model loading (exactly like live_trading.py)
    device = "cuda" if torch.cuda.is_available() else "cpu"
    training_settings = cfg["trainingSettings"]
    
    policy_kwargs = {
        "net_arch": training_settings["netArch"],
        "features_extractor_class": SimpleCNN1D,
        "features_extractor_kwargs": {
            **training_settings["featureExtractorKwargs"],
            "feature_names": feature_cols,
            "meta_len": 11
        }
    }
    
    custom_objects = {
        "buffer_size": training_settings["bufferSize"],
        "policy_kwargs": policy_kwargs,
        "replay_buffer_class": SafeReplayBuffer,
        "learning_rate": 0.0001
    }
    
    # Load model and vecnorm
    model = PopArtSAC.load(latest_model, device=device, custom_objects=custom_objects)

    # Create dummy environment for VecNormalize loading
    from scalping_env import ScalpingEnv
    dummy_data = pd.DataFrame({
        'open': [1.0], 'high': [1.0], 'low': [1.0], 'close': [1.0], 'volume': [1.0]
    }, index=[pd.Timestamp.now()])
    dummy_env = ScalpingEnv(dummy_data, cfg)
    vecnorm = VecNormalize.load(latest_vecnorm, DummyVecEnv([lambda: dummy_env]))
    
    log.info(f"✅ Model loaded: {model.observation_space.shape}")
    log.info(f"✅ VecNormalize loaded")
    
    # Initialize TradeExecutor with mock client
    mock_binance = MockBinanceClient()
    te = TradeExecutor(cfg, test_mode=True)
    
    log.info("✅ TradeExecutor initialized")
    
    # Prepare data for simulation
    log.info("📊 Preparing data for simulation...")
    
    # Convert to 5m data for indicators (if using 1s decisions)
    if use_1s_decisions:
        # Resample to 5m for indicators
        data_5m = historical_data.resample('5min').agg({
            'open': 'first',
            'high': 'max', 
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()
        
        # Forward fill 5m data to 1s
        data_1s = historical_data.copy()
        
        log.info(f"5m data: {len(data_5m)} rows")
        log.info(f"1s data: {len(data_1s)} rows")
    else:
        data_5m = historical_data.copy()
        data_1s = None
    
    # Calculate indicators on 5m data
    log.info("📊 Calculating indicators...")
    data_dict = {'5m': data_5m}
    if data_1s is not None:
        data_dict['1s'] = data_1s
    
    base_df, calculated_features = calculate_and_merge_indicators(
        data_dict, cfg,
        skip_hmm=not cfg.get("hmmSettings", {}).get("enabled", False),
        hmm_model_external=None,
        hmm_scaler_external=None
    )
    
    log.info(f"📊 Indicators calculated: {base_df.shape}")
    
    # Continue with simulation logic...
    return simulate_trading_loop(cfg, base_df, model, vecnorm, te, mock_binance,
                                use_1s_decisions, out_trades, out_equity)

def simulate_trading_loop(cfg: Dict[str, Any], base_df: pd.DataFrame,
                         model, vecnorm, te: TradeExecutor, mock_binance,
                         use_1s_decisions: bool, out_trades: str = None,
                         out_equity: str = None) -> Dict[str, Any]:
    """Main simulation loop that mimics live_trading.py logic"""

    log.info("🔄 Starting simulation loop...")

    # Get configuration
    EXPECTED_FEATURE_COLS = list(cfg["envSettings"]["feature_columns"])
    lookback = cfg["envSettings"].get("state_lookback", 30)

    log.info(f"📊 Using {len(EXPECTED_FEATURE_COLS)} features with {lookback} lookback")

    # Prepare data exactly like live_trading.py
    # Fill missing features with zeros
    missing_cols = [col for col in EXPECTED_FEATURE_COLS if col not in base_df.columns]
    if missing_cols:
        log.warning(f"Missing {len(missing_cols)} columns → filled with zeros: {', '.join(missing_cols)}")
        base_df = base_df.copy()
        for col in missing_cols:
            base_df[col] = 0.0

    # Apply forward fill
    base_df = base_df.copy()
    base_df[EXPECTED_FEATURE_COLS] = base_df[EXPECTED_FEATURE_COLS].ffill()

    # Apply log1p transformation to volume columns
    vol_cols = [c for c in EXPECTED_FEATURE_COLS if "vol" in c]
    if vol_cols:
        for col in vol_cols:
            if col in base_df.columns:
                base_df[col] = base_df[col].fillna(1e-8)
                base_df[col] = np.where(base_df[col] <= 0, 1e-8, base_df[col])
        base_df[vol_cols] = np.log1p(base_df[vol_cols])
        log.debug(f"Applied log1p to volume columns: {vol_cols}")

    # Replace infinities with NaN
    base_df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # Extract only the required features in correct order
    base_df = base_df[EXPECTED_FEATURE_COLS].astype("float32")

    # Fill remaining NaN values
    essential_features = [col for col in ['open', 'high', 'low', 'close', 'volume'] if col in EXPECTED_FEATURE_COLS]
    if len(base_df) > 0:
        nan_counts = base_df[essential_features].isna().sum()
        if nan_counts.sum() > 0:
            log.warning(f"Found NaN values in essential features: {nan_counts[nan_counts > 0].to_dict()}")
            base_df[essential_features] = base_df[essential_features].fillna(method='ffill').fillna(0)

    # Fill remaining NaN values in non-essential features with 0
    remaining_features = [col for col in EXPECTED_FEATURE_COLS if col not in essential_features]
    if remaining_features:
        base_df[remaining_features] = base_df[remaining_features].fillna(0)

    log.info(f"📊 Data prepared: {base_df.shape}")

    # Initialize tracking variables
    trades = []
    equity_curve = []
    decisions = []

    # Get model observation space
    expected_obs_size = model.observation_space.shape[0]
    feats_needed = (expected_obs_size - 11) // lookback

    log.info(f"🔧 Model expects {feats_needed} features, we have {len(EXPECTED_FEATURE_COLS)}")

    # Handle feature dimension mismatch exactly like live_trading.py
    if len(EXPECTED_FEATURE_COLS) != feats_needed:
        log.warning(f"⚠️ FEATURE DIMENSION MISMATCH:")
        log.warning(f"   Model expects {feats_needed} features but config has {len(EXPECTED_FEATURE_COLS)}")

        if len(EXPECTED_FEATURE_COLS) > feats_needed:
            log.warning(f"   Truncating {len(EXPECTED_FEATURE_COLS) - feats_needed} features to match model")
            selected_features = EXPECTED_FEATURE_COLS[:feats_needed]
            log.info(f"   Using first {feats_needed} features: {selected_features[:3]}...{selected_features[-3:]}")
        else:
            log.warning(f"   Padding with {feats_needed - len(EXPECTED_FEATURE_COLS)} zero features")
            selected_features = EXPECTED_FEATURE_COLS.copy()
            for i in range(feats_needed - len(EXPECTED_FEATURE_COLS)):
                selected_features.append(f"pad_feature_{i}")
                base_df[f"pad_feature_{i}"] = 0.0
            log.info(f"   Padded to {len(selected_features)} features")
    else:
        selected_features = EXPECTED_FEATURE_COLS
        log.info(f"   ✅ Feature count matches model expectations")

    # Ensure base_df contains only the selected features in correct order
    base_df = base_df[selected_features]
    log.info(f"📊 Final feature selection: {len(selected_features)} features")

    # Start simulation loop
    log.info(f"🔄 Starting simulation with {len(base_df)} data points...")

    # Skip initial lookback period
    start_idx = lookback
    total_steps = len(base_df) - start_idx

    log.info(f"📊 Processing {total_steps} steps (skipping first {lookback} for lookback)")

    for i in range(start_idx, len(base_df)):
        current_time = base_df.index[i]
        current_price = base_df.iloc[i]['close'] if 'close' in base_df.columns else base_df.iloc[i][selected_features[3]]  # Assume 4th feature is close

        # Extract features for state vector (exactly like live_trading.py)
        frame = base_df.to_numpy(dtype=np.float32)[i-lookback:i]

        if frame.shape[0] != lookback:
            log.warning(f"Insufficient data for lookback at index {i}")
            continue

        if frame.shape[1] != len(selected_features):
            log.error(f"Feature dimension mismatch: expected {len(selected_features)}, got {frame.shape[1]}")
            continue

        # Create state vector exactly like live_trading.py
        # Add meta features (7 trade + 4 time features)
        pos = te.pos if hasattr(te, 'pos') else 0
        size = te.size if hasattr(te, 'size') else 0.0
        entry_price = te.entry_price if hasattr(te, 'entry_price') else current_price

        # Trade meta features (7)
        trade_meta = np.array([
            pos,                    # position (-1, 0, 1)
            size,                   # position size
            entry_price,            # entry price
            0.0,                    # sl_price (will be set by TE)
            0.0,                    # tp_price (will be set by TE)
            0.0,                    # unrealized_pnl (will be calculated)
            0.0                     # time_in_position (will be calculated)
        ], dtype=np.float32)

        # Time meta features (4)
        hour = current_time.hour / 23.0
        minute = current_time.minute / 59.0
        day_of_week = current_time.weekday() / 6.0
        day_of_month = current_time.day / 31.0

        time_meta = np.array([hour, minute, day_of_week, day_of_month], dtype=np.float32)

        # Combine all features
        flattened_frame = frame.flatten()
        state = np.concatenate([flattened_frame, trade_meta, time_meta])

        # Verify state dimensions
        if len(state) != expected_obs_size:
            log.error(f"State dimension mismatch: expected {expected_obs_size}, got {len(state)}")
            continue

        # Normalize state
        state_normalized = vecnorm.normalize_obs(state.reshape(1, -1))[0]

        # Get model prediction
        action, _ = model.predict(state_normalized, deterministic=True)

        # Process action exactly like live_trading.py
        action_clipped = np.clip(action.flatten(), -1.0, 1.0)
        entry_sig = action_clipped[0]
        a_tp = action_clipped[2]
        exit_sig = action_clipped[3]

        # Log decision
        decision = {
            'timestamp': current_time,
            'price': current_price,
            'position': pos,
            'entry_sig': entry_sig,
            'exit_sig': exit_sig,
            'action': action_clipped.tolist()
        }
        decisions.append(decision)

        # Execute trading logic through TradeExecutor
        # This mimics the exact call from live_trading.py
        try:
            te.execute_with_binance(mock_binance, action, current_price, 0.0)  # atr_now = 0.0 for simplicity
        except Exception as e:
            log.warning(f"TradeExecutor error at {current_time}: {e}")
            continue

        # Track trades and equity
        # Get current balance from TradeExecutor
        current_balance = getattr(te, 'balance', cfg["account"]["initialEquity"])

        # Calculate unrealized PnL
        unrealized_pnl = 0.0
        if pos != 0 and entry_price > 0:
            unrealized_pnl = (current_price - entry_price) * size * pos

        # Calculate current equity
        current_equity = current_balance + unrealized_pnl

        equity_point = {
            'timestamp': current_time,
            'equity': current_equity,
            'balance': current_balance,
            'position': pos,
            'unrealized_pnl': unrealized_pnl
        }
        equity_curve.append(equity_point)

        # Progress logging
        if i % 1000 == 0:
            progress = (i - start_idx) / total_steps * 100
            log.info(f"📊 Progress: {progress:.1f}% ({i-start_idx}/{total_steps}) - "
                    f"Equity: ${current_equity:.2f}, Position: {pos}")

    log.info("✅ Simulation completed!")
    log.info(f"📊 Total trades: {len(trades)}")
    log.info(f"📊 Final equity: ${equity_curve[-1]['equity']:.2f}" if equity_curve else "No equity data")

    # Save results if requested
    if out_trades and trades:
        trades_df = pd.DataFrame(trades)
        trades_df.to_csv(out_trades, index=False)
        log.info(f"💾 Trades saved to: {out_trades}")

    if out_equity and equity_curve:
        equity_df = pd.DataFrame(equity_curve)
        equity_df.set_index('timestamp', inplace=True)
        equity_df.to_csv(out_equity)
        log.info(f"💾 Equity curve saved to: {out_equity}")

    return {
        "trades": trades,
        "equity": equity_curve,
        "decisions": decisions,
        "final_equity": equity_curve[-1]['equity'] if equity_curve else 0,
        "total_trades": len(trades)
    }

def main() -> None:
    """Main entry point for simulation"""
    parser = argparse.ArgumentParser("Simulate live trading logic with historical data")
    parser.add_argument("--cfg", type=Path, default=DEFAULT_CONFIG_PATH,
                        help="Path to configuration file")
    parser.add_argument("--start", type=str, required=True,
                        help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end", type=str, required=True,
                        help="End date (YYYY-MM-DD)")
    parser.add_argument("--out-trades", type=str,
                        help="Output file for trades CSV")
    parser.add_argument("--out-equity", type=str,
                        help="Output file for equity curve CSV")
    parser.add_argument("--log-level", default="INFO",
                        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                        help="Logging level")

    args = parser.parse_args()

    # Set logging level
    log.setLevel(args.log_level.upper())

    try:
        # Load configuration
        cfg = load_config(args.cfg)

        # Get symbol from config
        symbol = cfg.get("symbol") or cfg.get("dataProvider", {}).get("symbol", "XRPUSDC")
        timeframe = cfg["primaryTimeframe"]

        log.info(f"🚀 Starting live trading simulation")
        log.info(f"📊 Symbol: {symbol}")
        log.info(f"📊 Timeframe: {timeframe}")
        log.info(f"📊 Date range: {args.start} to {args.end}")

        # Load historical data
        historical_data = load_historical_data(symbol, args.start, args.end, timeframe)

        # Run simulation
        results = simulate_live_trading_logic(
            cfg=cfg,
            historical_data=historical_data,
            start_date=args.start,
            end_date=args.end,
            out_trades=args.out_trades,
            out_equity=args.out_equity
        )

        # Print summary
        log.info("=" * 60)
        log.info("🏁 SIMULATION RESULTS")
        log.info("=" * 60)
        log.info(f"📊 Total trades: {results['total_trades']}")
        log.info(f"💰 Final equity: ${results['final_equity']:.2f}")

        if results['total_trades'] > 0:
            # Calculate some basic stats
            trades_df = pd.DataFrame(results['trades'])
            if 'pnl' in trades_df.columns:
                total_pnl = trades_df['pnl'].sum()
                win_rate = (trades_df['pnl'] > 0).mean() * 100
                log.info(f"📈 Total PnL: ${total_pnl:.2f}")
                log.info(f"🎯 Win rate: {win_rate:.1f}%")

        if args.out_trades:
            log.info(f"💾 Trades saved to: {args.out_trades}")
        if args.out_equity:
            log.info(f"💾 Equity curve saved to: {args.out_equity}")

        log.info("✅ Simulation completed successfully!")

    except Exception as e:
        log.error(f"❌ Simulation failed: {e}")
        import traceback
        log.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
