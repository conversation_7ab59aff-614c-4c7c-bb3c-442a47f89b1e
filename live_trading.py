#!/usr/bin/env python3
# live_trading.py
"""
Live‑trading slučka s Agentom SAC a TradeExecutor‑om,
ktorý používa identickú risk‑/TP/SL logiku ako backtest.
"""
from __future__ import annotations
import os, sys, time, json, logging, argparse, threading, asyncio
from pathlib import Path
from datetime import datetime, timedelta, timezone
from queue import Queue, Empty
from typing import Dict, Any

import numpy as np
import pandas as pd
import torch, requests, websockets
from websockets.exceptions import ConnectionClosedError

# Import our new modules
from config_loader import ConfigLoader
from fallback_data_provider import FallbackDataProvider

try:
    from binance.client import Client as BinanceClient
    BINANCE_AVAILABLE = True
except ImportError:
    BINANCE_AVAILABLE = False
    BinanceClient = None
    print("WARNING: python-binance not installed. Live trading disabled.")
    print("Install with: pip install python-binance")
from stable_baselines3 import SAC
from stable_baselines3.common.vec_env import V<PERSON><PERSON><PERSON><PERSON><PERSON>, DummyVecEnv

from scalping_env import ScalpingEnv
from indicators import calculate_and_merge_indicators
from trade_executor import TradeExecutor                # <‑‑ náš nový modul
from data_provider import DataProvider
from popart_sac import PopArtSAC                        # <-- PopArt SAC support
from agent import SimpleCNN1D, SafeReplayBuffer         # <-- Pre PopArt loading

# ──────────────────────────── LOGGING ─────────────────────────────────────
logging.basicConfig(
    level   = logging.INFO,
    format  = "%(asctime)s [%(name)s] [%(levelname)s] %(message)s",
    datefmt = "%Y-%m-%d %H:%M:%S",
    handlers=[logging.StreamHandler(sys.stdout),
              logging.FileHandler("live_trading.log")]
)
log = logging.getLogger("LiveTrading")

def safe_read_parquet(file_path, **kwargs):
    """
    Safe wrapper around pd.read_parquet that validates data quality.
    Detects and handles corrupted market data with 0.00000 prices.
    """
    try:
        df = pd.read_parquet(file_path, **kwargs)
        
        # Check if this is market OHLC data
        ohlc_cols = ['open', 'high', 'low', 'close']
        available_ohlc = [col for col in ohlc_cols if col in df.columns]
        
        if available_ohlc:
            # Check for corrupted 0.00000 prices in OHLC data
            for col in available_ohlc:
                zero_mask = (df[col] == 0.0) | (df[col].isna())
                if zero_mask.any():
                    corrupted_count = zero_mask.sum()
                    total_rows = len(df)
                    
                    if corrupted_count > 0:
                        log.warning(f"⚠️ DATA CORRUPTION DETECTED in {file_path}")
                        log.warning(f"   Column '{col}' has {corrupted_count}/{total_rows} corrupted (0.00000) values")
                        
                        # Show first few corrupted timestamps for debugging
                        if 'timestamp' in df.columns:
                            corrupted_rows = df[zero_mask]
                            for idx, row in corrupted_rows.head(3).iterrows():
                                timestamp = pd.to_datetime(row['timestamp'], utc=True) if pd.notna(row['timestamp']) else 'Unknown'
                                ohlc_vals = {c: row.get(c, 'N/A') for c in available_ohlc}
                                log.warning(f"   {timestamp}: {ohlc_vals}")
                        
                        # CRITICAL: Remove corrupted rows to prevent trading losses
                        # Only remove rows where ANY OHLC value is 0.0 or NaN
                        corruption_mask = pd.Series(False, index=df.index)
                        for ohlc_col in available_ohlc:
                            corruption_mask |= (df[ohlc_col] == 0.0) | (df[ohlc_col].isna())
                        
                        if corruption_mask.any():
                            clean_df = df[~corruption_mask].copy()
                            removed_count = len(df) - len(clean_df)
                            log.warning(f"   🧹 CLEANED: Removed {removed_count} corrupted rows")
                            log.warning(f"   ✅ SAFE DATA: {len(clean_df)} clean rows remaining")
                            df = clean_df
                        
                        # If too much data is corrupted, raise error
                        if len(df) < total_rows * 0.5:  # More than 50% corrupted
                            raise ValueError(f"File {file_path} has too much corrupted data ({corrupted_count}/{total_rows} rows). Cannot proceed safely.")
        
        return df
        
    except Exception as e:
        log.error(f"Error reading {file_path}: {e}")
        raise

DEFAULT_CONFIG_PATH = "strategyConfig_scalp_1s.json"
COINAPI_WS_URL      = "wss://ws.coinapi.io/v1/"
COINAPI_REST_URL    = "https://rest.coinapi.io/v1/ohlcv/{symbol}/history"
STREAM_QUEUE: Queue = Queue()

# ──────────────────────────── POMOCNÉ FUNKCIE ─────────────────────────────
def load_config(path: Path) -> Dict[str, Any]:
    """Load configuration with environment variable substitution (compatible with simulate_trading_new.py)."""
    import yaml
    import os
    import re
    
    log.info(f"Loading config from: {path}")
    try:
        with open(path, 'r') as f:
            if path.suffix.lower() == '.json':
                content = f.read()
                # Replace environment variables
                def replace_env_var(match):
                    var_name = match.group(1)
                    return os.environ.get(var_name, match.group(0))
                content = re.sub(r'\$\{([^}]+)\}', replace_env_var, content)
                cfg = json.loads(content)
            elif path.suffix.lower() in ['.yaml', '.yml']:
                cfg = yaml.safe_load(f)
            else:
                raise ValueError(f"Unsupported config format: {path.suffix}")
    except Exception as e:
        log.error(f"Error loading/processing config {path}: {e}")
        raise

    # DEBUG: Check what API key is actually being used
    api_key = cfg.get("coinapi", {}).get("apiKey", "NOT_FOUND")
    if api_key and not api_key.startswith("${"):
        masked_key = f"{api_key[:8]}...{api_key[-4:]}" if len(api_key) > 12 else "INVALID_KEY"
        log.info(f"🔍 DEBUG: Using CoinAPI key: {masked_key}")
    elif api_key and api_key.startswith("${"):
        log.error(f"❌ ENV VARIABLE NOT SUBSTITUTED: {api_key}")
        log.error("❌ This will cause authentication failures!")
    
    log.info(f"Config loaded for symbol: {cfg.get('symbol', 'N/A')}")
    return cfg

# ―――――――――――――――――――――――― WEBSOCKET STREAM ―――――――――――――――――――――――――
async def coinapi_stream(symbol: str, tf: str, api_key: str) -> None:
    while True:
        try:
            async with websockets.connect(
                COINAPI_WS_URL,
                ping_interval=30,
                ping_timeout=10,
            ) as ws:
                # (re)subscribe
                await ws.send(json.dumps({
                    "type": "hello",
                    "apikey": api_key,
                    "heartbeat": True,          # odporúčam True
                    "subscribe_data_type": ["ohlcv", "trade", "book"],
                    "subscribe_filter_symbol_id": [symbol],
                    "subscribe_data_format": "JSON",
                    "interval": tf
                }))
                log.info("WS subscribed")
                async for m in ws:
                    try:
                        data = json.loads(m)
                        kind = data.get("type")
                        if kind == "ohlcv":
                            STREAM_QUEUE.put(("ohlcv", data))
                        elif kind == "trade":
                            STREAM_QUEUE.put(("trade", data))
                        elif kind == "book":
                            STREAM_QUEUE.put(("orderbook", data))
                    except Exception as e:      # noqa: BLE001
                        log.error(f"Stream parse error: {e}")
        except ConnectionClosedError as e:
            log.warning(f"WS connection closed, reconnect in 5s: {e}")
            await asyncio.sleep(5)
            continue
        except Exception as e:
            log.error(f"WS unexpected error: {e}, reconnect in 10s")
            await asyncio.sleep(10)
            continue

def start_stream_thread(symbol: str, tf: str, api_key: str) -> None:
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(coinapi_stream(symbol, tf, api_key))

# ──────────────────────────── MAIN ENTRY ──────────────────────────────────
def main() -> None:
    parser = argparse.ArgumentParser("Live trading with SAC + TradeExecutor")
    parser.add_argument("--cfg", type=Path, default=DEFAULT_CONFIG_PATH)
    parser.add_argument("--log-level", default="INFO",
                        choices=["DEBUG", "INFO", "WARNING", "ERROR"])
    parser.add_argument("--use-1s-decisions", "--use-1s-decision", action="store_true",
                        help="Use 1s decision frequency with 5m features (forward-filled)")
    parser.add_argument("--test-trade", action="store_true",
                        help="Force one test LONG trade to verify TradeExecutor functionality")
    args = parser.parse_args()

    log.setLevel(args.log_level.upper())
    cfg = load_config(args.cfg)

    # Compatible with strategyConfig_scalp_1s.json structure
    symbol_cfg   = cfg.get("symbol") or cfg.get("dataProvider", {}).get("symbol", "XRPUSDC")
    timeframe    = cfg["primaryTimeframe"]                # napr. 1s alebo 5m
    coinapi_key  = cfg["coinapi"]["apiKey"]
    use_1s_decisions = args.use_1s_decisions or cfg.get("use_1s_decisions", False)
    test_trade_mode = args.test_trade
    
    # Check for missing API keys
    if not coinapi_key or coinapi_key.startswith("${"):
        log.error("❌ CoinAPI key not configured!")
        log.error("Please set COINAPI_KEY environment variable or create .env file")
        log.error("See .env.example for configuration template")
        sys.exit(1)

    if use_1s_decisions:
        log.info("🚀 Using 1s decision frequency with 5m features (forward-filled)")
        decision_timeframe = "1s"
    else:
        log.info(f"Using {timeframe} decision frequency")
        decision_timeframe = timeframe

    if test_trade_mode:
        log.info("🧪 TEST TRADE MODE ENABLED")
        log.info("🧪 Will force one LONG trade to verify TradeExecutor functionality")
        log.info("🧪 Expected: Entry signal=0.8 > 0.5 threshold → LONG position with SL/TP")

    # Binance client (odovzdáme ho aj TradeExecutor‑u)
    if not BINANCE_AVAILABLE:
        log.error("❌ Binance client not available - python-binance not installed!")
        log.error("Install with: pip install python-binance")
        sys.exit(1)
    
    # Handle Binance API keys from environment variables
    binance_api_key = cfg["binance"]["apiKey"]
    binance_api_secret = cfg["binance"]["apiSecret"]
    
    # Replace environment variables if needed
    if binance_api_key.startswith("${"):
        var_name = binance_api_key.replace("${", "").replace("}", "")
        binance_api_key = os.environ.get(var_name, "")
    if binance_api_secret.startswith("${"):
        var_name = binance_api_secret.replace("${", "").replace("}", "")
        binance_api_secret = os.environ.get(var_name, "")
    
    binance = BinanceClient(binance_api_key, binance_api_secret)
    
    # Setup futures trading if enabled
    if cfg.get("binance", {}).get("futures", False):
        log.info("🚀 Setting up Binance Futures trading...")
        
        # Get futures settings
        leverage = cfg.get("binance", {}).get("leverage", 10)
        margin_type = cfg.get("binance", {}).get("marginType", "ISOLATED")
        symbol = symbol_cfg  # Use the symbol from config root level
        
        # Extract futures symbol from CoinAPI format if needed
        if symbol.startswith('BINANCEFTS_PERP_'):
            parts = symbol.replace('BINANCEFTS_PERP_', '').split('_')
            symbol = ''.join(parts) if len(parts) >= 2 else symbol
        
        try:
            # Set leverage for futures trading
            if not cfg.get("testMode", False):  # Only set leverage in live mode
                try:
                    binance.futures_change_leverage(symbol=symbol, leverage=leverage)
                    log.info(f"✅ Set leverage to {leverage}x for {symbol}")
                except Exception as e:
                    log.warning(f"Failed to set leverage: {e}")
                    if "does not exist" in str(e).lower():
                        log.error(f"❌ Symbol {symbol} does not exist in futures market")
                    # Continue without failing
                
                # Set margin type with better error handling
                try:
                    binance.futures_change_margin_type(symbol=symbol, marginType=margin_type)
                    log.info(f"✅ Set margin type to {margin_type} for {symbol}")
                except Exception as e:
                    log.warning(f"Failed to set margin type: {e}")
                    # Check for specific credit/balance issues
                    if "credit status" in str(e).lower() or "insufficient" in str(e).lower():
                        log.warning(f"⚠️ Account credit/balance issue - continuing with default margin mode")
                        log.warning(f"   You may need to verify account status or add more balance")
                    elif "mode" in str(e).lower():
                        log.warning(f"⚠️ Margin mode setting failed - using account default")
                    # Continue without failing - margin mode is often optional
                    
            else:
                log.info(f"🧪 TEST MODE: Would set leverage={leverage}x, margin={margin_type} for {symbol}")
                
        except Exception as e:
            log.warning(f"General futures setup error: {e}")
            log.warning("⚠️ Futures setup had issues but continuing - trading may still work")
            # Don't fail completely - many futures settings are optional
    
    cfg["binance_client"] = binance                       # uložíme do cfg

    # ------------------------- CoinAPI symbol (compatible with strategyConfig_scalp_1s.json) -----------------------------
    if symbol_cfg.startswith("BINANCEFTS_PERP_"):
        coinapi_symbol = symbol_cfg
    else:
        # For symbols like "XRPUSDC", convert to CoinAPI format
        quotes = ["USDC", "USDT", "USD", "BUSD", "BTC", "ETH", "EUR"]
        for q in quotes:
            if symbol_cfg.endswith(q):
                base  = symbol_cfg.replace("/", "")[:-len(q)]
                quote = q
                break
        else:                       # fallback: prvé 3 + zvyšok
            base, quote = symbol_cfg[:3], symbol_cfg[3:]
        coinapi_symbol = f"BINANCEFTS_PERP_{base}_{quote}"
    
    log.info(f"🔄 Symbol mapping: Config='{symbol_cfg}' → CoinAPI='{coinapi_symbol}'")

    # ------------------------- dáta (historické) --------------------------
    dp = DataProvider(cfg)
    # Use test mode from config or command line argument
    test_mode_enabled = cfg.get("testMode", False) or test_trade_mode
    te = TradeExecutor(cfg, test_mode=test_mode_enabled)
    
    log.info(f"🔄 Configuration loaded:")
    log.info(f"   Symbol: {symbol_cfg}")
    log.info(f"   Primary Timeframe: {timeframe}")
    log.info(f"   Use 1s decisions: {use_1s_decisions}")
    log.info(f"   Test mode: {test_mode_enabled}")

    if test_mode_enabled:
        log.info("🧪 TradeExecutor initialized in TEST MODE - no real orders will be placed")
        # Reset daily limits for testing
        te.force_reset_daily_limits()
    else:
        log.info("💰 TradeExecutor initialized in LIVE MODE - real orders will be placed!")
    
    # Model path detection with .zip handling
    model_path_str = cfg["trainingSettings"]["modelSavePath"]
    if not model_path_str.endswith(".zip"):
        model_path_str += ".zip"
    model_path = Path(model_path_str)
    
    # Auto-detect available model if specified doesn't exist
    if not model_path.exists():
        available_models = list(Path(".").glob("sac_*_steps.zip"))
        if available_models:
            # Sort by timesteps and take the latest
            available_models.sort(key=lambda x: int(x.stem.split('_')[1]))
            model_path = available_models[-1]
            log.info(f"Auto-detected model: {model_path}")
        else:
            log.error(f"No model found at {model_path} and no auto-detectable models!")
            sys.exit(1)
    
    # VecNormalize path detection
    vecnorm_path = model_path.with_suffix('.vecnorm.pkl')
    if not vecnorm_path.exists():
        model_stem = model_path.stem.replace('_steps', '')
        vecnorm_path = Path(f"{model_stem}.vecnorm.pkl")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    log.info(f"Using model: {model_path}")
    log.info(f"Using vecnorm: {vecnorm_path}")

    # --- Calculate expected observation space size like simulate_trading_new.py ---
    feature_cols = cfg["envSettings"]["feature_columns"]
    lookback = cfg["envSettings"].get("state_lookback", 30)
    
    # Calculate expected observation space: features * lookback + meta_features
    # Meta features: 7 trade features + 4 time features = 11 total
    expected_obs_size = len(feature_cols) * lookback + 11
    
    log.info(f"🔧 Environment setup:")
    log.info(f"   Feature columns: {len(feature_cols)} features")
    log.info(f"   Lookback: {lookback}")
    log.info(f"   Expected observation space: {expected_obs_size}")
    log.info(f"   Features sample: {feature_cols[:5]}{'...' if len(feature_cols) > 5 else ''}")
    
    # Create dummy environment with correct observation space
    dummy_df = pd.DataFrame({c: [0.0] for c in feature_cols},
                             index=[pd.Timestamp.now(tz="UTC")])
    dummy_env = DummyVecEnv([lambda: ScalpingEnv(dummy_df, cfg)])
    
    # Load VecNormalize if available - with proper error handling for shape mismatch
    vecnorm = None
    if vecnorm_path.exists():
        try:
            vecnorm = VecNormalize.load(vecnorm_path, venv=dummy_env)
            vecnorm.training = False
            log.info("✅ VecNormalize loaded successfully")
            
            # Verify observation space compatibility
            if hasattr(vecnorm, 'observation_space'):
                vecnorm_obs_shape = vecnorm.observation_space.shape[0] if hasattr(vecnorm.observation_space, 'shape') else None
                if vecnorm_obs_shape and vecnorm_obs_shape != expected_obs_size:
                    log.warning(f"⚠️ VecNormalize observation space mismatch:")
                    log.warning(f"   VecNormalize expects: {vecnorm_obs_shape}")
                    log.warning(f"   Current config expects: {expected_obs_size}")
                    log.warning(f"   This may cause prediction errors!")
            
        except Exception as e:
            log.warning(f"Failed to load VecNormalize: {e}")
            if "spaces must have the same shape" in str(e):
                log.warning(f"⚠️ VecNormalize shape mismatch - model was trained with different feature set")
                log.warning(f"   Model expectation vs current config mismatch")
                log.warning(f"   Consider using the exact same config as during training")
            vecnorm = None
    else:
        log.warning(f"VecNormalize not found: {vecnorm_path}")

    # Load final model with correct configuration
    log.info("📦 Loading final PopArt SAC model...")
    
    # Get training settings from config
    training_settings = cfg.get("trainingSettings", {})
    
    # Policy kwargs for PopArt SAC with corrected feature columns
    policy_kwargs = {
        "net_arch": training_settings.get("netArch", [512, 256]),
        "features_extractor_class": SimpleCNN1D,
        "features_extractor_kwargs": {
            **training_settings.get("featureExtractorKwargs", {
                "lookback": 30,
                "n_filters": 128,
                "kernel": 3,
                "out_dim": 256
            }),
            "feature_names": feature_cols,  # Use corrected feature_cols
            "meta_len": 11
        }
    }
    
    # Initialize custom_objects dictionary
    custom_objects = {
        "buffer_size": training_settings.get("bufferSize", 5000000),
        "policy_kwargs": policy_kwargs,
        "replay_buffer_class": SafeReplayBuffer,
        "learning_rate": 0.0001
    }

    try:
        model = PopArtSAC.load(model_path, device=device, custom_objects=custom_objects)
        log.info(f"✅ PopArt SAC model loaded successfully from {model_path}")
        log.info(f"   Model observation space: {model.observation_space.shape}")
        log.info(f"   Using {len(feature_cols)} features for compatibility")
    except Exception as e:
        log.error(f"❌ Failed to load PopArt model: {e}")
        log.warning("🤖 Using DummyModel fallback for testing")
        class DummyModel:                       # pylint: disable=too-few-public-methods
            def predict(self, obs, deterministic=True):
                return np.zeros(4, dtype=np.float32), None
            @property
            def observation_space(self):
                from gym.spaces import Box
                return Box(low=-np.inf, high=np.inf, shape=(expected_obs_size,))
        model = DummyModel()

    # ------------------------- štart FALLBACK DATA PROVIDER ----------------------------
    log.info("🔄 Starting Fallback Data Provider (CoinAPI + Binance fallback)...")
    
    # Create enhanced data queue for fallback provider
    ENHANCED_STREAM_QUEUE = Queue()
    
    # Initialize fallback data provider
    fallback_provider = FallbackDataProvider(cfg, ENHANCED_STREAM_QUEUE)
    
    # Start data streams with fallback
    timeframes_needed = [timeframe]
    if use_1s_decisions and "1s" not in timeframes_needed:
        timeframes_needed.append("1s")
    
    # CRITICAL FIX: Add timeframes needed for indicators
    # Scan indicator settings to find required timeframes
    indicator_settings = cfg.get("indicatorSettings", {})
    for indicator_name, indicator_config in indicator_settings.items():
        if isinstance(indicator_config, dict) and indicator_config.get("enabled", True):
            indicator_tf = indicator_config.get("tf", timeframe)
            if indicator_tf not in timeframes_needed:
                timeframes_needed.append(indicator_tf)
                log.info(f"🔧 Added timeframe '{indicator_tf}' for indicator '{indicator_name}'")
    
    # Remove duplicates
    timeframes_needed = list(set(timeframes_needed))
    
    fallback_provider.start_streams(timeframes_needed)
    log.info(f"✅ Data streams started for timeframes: {timeframes_needed}")

    # ------------------------- LOAD HISTORICAL DATA ---------------------------
    log.info("🔄 Loading historical data from parquet files before starting WebSocket...")
    
    # Load historical OHLCV data (3 days back to ensure enough data for indicators)
    end_time = datetime.now(timezone.utc)
    start_time = end_time - timedelta(days=3)
    
    try:
        historical_5m = dp.load_historical_data(start_time, end_time, lookback_hours=72)
        log.info(f"✅ Loaded {len(historical_5m)} historical {timeframe} bars from parquet files")
    except Exception as e:
        log.warning(f"⚠️ Failed to load historical data from parquet: {e}")
        historical_5m = pd.DataFrame()
    
    # Try to load 1s historical data if using 1s decisions
    historical_1s = pd.DataFrame()
    if use_1s_decisions:
        try:
            # Load 1s data from parquet if available (DataProvider doesn't support timeframe parameter)
            historical_1s = dp.load_historical_data(start_time, end_time, lookback_hours=72)
            if not historical_1s.empty:
                log.info(f"✅ Loaded {len(historical_1s)} historical 1s bars from parquet files")
            else:
                log.info("ℹ️ No 1s historical data in parquet - will use live stream only")
        except Exception as e:
            log.warning(f"⚠️ Failed to load 1s historical data: {e}")
            historical_1s = pd.DataFrame()
        
        log.info("ℹ️ 1s decision mode: will forward-fill 5m features to 1s resolution")
        # Note: For 1s decisions, we use 5m historical data and forward-fill features
        # Live 1s data will be streamed via WebSocket
        
    # Wait a moment for initial 5m data to load
    log.info("🔄 Waiting for initial 5m data to load...")
    time.sleep(5)  # Give the REST API time to load initial data
    log.info("✅ Proceeding with live trading")
    
    # ------------------------- init dátový dict ---------------------------
    data_dict: Dict[str, pd.DataFrame] = {
        "trades"    : pd.DataFrame(columns=["price", "size", "side"]),
        "orderbooks": pd.DataFrame(columns=[f"{s}{l}"
                                for l in range(1, 6)
                                for s in ("bid_px", "bid_qty", "ask_px", "ask_qty")])
    }
    
    # CRITICAL FIX: Properly initialize timeframe data based on actual timeframes
    if use_1s_decisions:
        # For 1s decisions, we need both 1s and 5m data
        data_dict["1s"] = historical_1s         # Pre-populated with historical 1s OHLCV
        data_dict["5m"] = historical_5m         # Pre-populated with historical 5m OHLCV
        log.info("✅ Initialized data_dict with 1s and 5m timeframes")
    else:
        # For non-1s decisions, use the primary timeframe
        data_dict[timeframe] = historical_5m    # Primary timeframe data
        log.info(f"✅ Initialized data_dict with {timeframe} timeframe")
    
    # Add any additional timeframes needed for indicators
    for tf in timeframes_needed:
        if tf not in data_dict:
            data_dict[tf] = pd.DataFrame()      # Empty DataFrame for live data
            log.info(f"✅ Added empty DataFrame for timeframe '{tf}'")

    lookback = cfg["envSettings"].get("state_lookback", 30)
    last_bar_ts = historical_5m.index[-1] if not historical_5m.empty else pd.Timestamp.utcnow()
    last_decision_ts = pd.Timestamp.utcnow()
    test_trade_executed = False  # Flag to ensure test trade only executes once

    # Feature smoothing for gradual updates when new 5m data arrives
    previous_features = None
    feature_smoothing_alpha = 0.3  # Smoothing factor for gradual feature updates
    
    # ●●●●●●●● INTELLIGENT EVALUATION TRACKING ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
    last_5m_data_ts = historical_5m.index[-1] if not historical_5m.empty else None
    last_1s_data_ts = historical_1s.index[-1] if not historical_1s.empty else None
    last_indicators_hash = None  # Hash of key indicators to detect changes
    # Adjust evaluation intervals based on decision mode
    if use_1s_decisions:
        min_evaluation_interval = 1.0   # 1 second for 1s decisions
        max_evaluation_interval = 10.0  # 10 seconds failsafe for 1s mode
    else:
        min_evaluation_interval = 5.0   # 5 seconds for 5m decisions
        max_evaluation_interval = 60.0  # 60 seconds failsafe for 5m mode
    last_evaluation_time = pd.Timestamp.utcnow()
    
    log.info(f"🎯 INTELLIGENT EVALUATION SETUP:")
    log.info(f"   Min evaluation interval: {min_evaluation_interval}s")
    log.info(f"   Max evaluation interval: {max_evaluation_interval}s")
    log.info(f"   Last 5m data timestamp: {last_5m_data_ts}")
    log.info(f"   Last 1s data timestamp: {last_1s_data_ts}")
    
    # Log data status
    if not historical_5m.empty:
        log.info(f"✅ Historical {timeframe} data loaded: {len(historical_5m)} bars from {historical_5m.index[0]} to {historical_5m.index[-1]}")
    else:
        log.warning(f"⚠️ No historical {timeframe} data - will wait for WebSocket data")
        
    if use_1s_decisions and not historical_1s.empty:
        log.info(f"✅ Historical 1s data loaded: {len(historical_1s)} bars from {historical_1s.index[0]} to {historical_1s.index[-1]}")
    elif use_1s_decisions:
        log.warning("⚠️ No historical 1s data - will wait for WebSocket data")

    # ──────────────────────── HLAVNÁ SLUČKA ───────────────────────────────
    while True:
        try:
            # Get message from enhanced queue (includes source info)
            queue_data = ENHANCED_STREAM_QUEUE.get(timeout=30)
            if len(queue_data) >= 3:
                typ, msg, source = queue_data[0], queue_data[1], queue_data[2]
                timeframe_received = queue_data[3] if len(queue_data) > 3 else timeframe
            else:
                # Fallback to old format
                typ, msg = queue_data[0], queue_data[1]
                source = "unknown"
                timeframe_received = timeframe
                
            # Log data source for monitoring (reduced frequency for high-volume streams)
            if source == "binance":
                if typ == "orderbook":
                    # Log orderbook messages less frequently (every 100th message)
                    if not hasattr(te, '_orderbook_log_counter'):
                        te._orderbook_log_counter = 0
                    te._orderbook_log_counter += 1
                    if te._orderbook_log_counter % 100 == 0:
                        log.debug(f"📊 Using Binance fallback data: {typ} (#{te._orderbook_log_counter})")
                elif typ == "trade":
                    # Log trade messages less frequently (every 50th message)
                    if not hasattr(te, '_trade_log_counter'):
                        te._trade_log_counter = 0
                    te._trade_log_counter += 1
                    if te._trade_log_counter % 50 == 0:
                        log.debug(f"📊 Using Binance fallback data: {typ} (#{te._trade_log_counter})")
                else:
                    log.debug(f"📊 Using Binance fallback data: {typ}")
            
        except Empty:
            # Check provider status during timeouts
            if hasattr(fallback_provider, 'get_status'):
                status = fallback_provider.get_status()
                if status.get('primary_failed') and not status.get('binance_active'):
                    log.warning("⚠️ Both CoinAPI and Binance streams inactive!")
                elif status.get('active_source') == 'binance':
                    log.info("📡 Running on Binance fallback data stream")
            continue

        # ●●●●●●●● 1) PARSOVANIE STREAMU → data_dict ●●●●●●●●●●●●●●●●●●●●●
        new_5m_data = False
        new_1s_data = False
        
        if typ == "ohlcv":
            # Handle both CoinAPI and Binance formatted timestamps
            timestamp_field = "time_period_end"
            if timestamp_field not in msg and "time_period_start" in msg:
                # For Binance data, we might need to convert
                if isinstance(msg.get("time_period_start"), (int, float)):
                    # Convert millisecond timestamp to datetime
                    ts = pd.to_datetime(msg["time_period_end"], unit='ms', utc=True)
                else:
                    ts = pd.to_datetime(msg["time_period_end"], utc=True)
            else:
                ts = pd.to_datetime(msg[timestamp_field], utc=True)
            # Extract OHLCV data with error handling
            try:
                row = {
                    "open": float(msg["price_open"]),
                    "high": float(msg["price_high"]),
                    "low": float(msg["price_low"]),
                    "close": float(msg["price_close"]),
                    "volume": float(msg["volume_traded"])
                }
                df = pd.DataFrame([row], index=[ts])
            except (KeyError, ValueError, TypeError) as e:
                log.warning(f"Failed to parse OHLCV data from {source}: {e}")
                continue
            
            # Use the actual timeframe from queue data (more reliable than detection)
            detected_tf = timeframe_received  # Use the timeframe from fallback provider
            
            # Ensure 5m data goes to the correct key regardless of primary timeframe
            if timeframe_received == "5m":
                detected_tf = "5m"
            elif timeframe_received == "1s":
                detected_tf = "1s"
            elif use_1s_decisions and timeframe_received == timeframe:
                # For primary timeframe data when using 1s decisions
                # Check if this looks like 1s data (frequent updates) or 5m data (sparse updates)
                if len(data_dict.get("1s", pd.DataFrame())) > 0:
                    last_1s_ts = data_dict["1s"].index[-1]
                    time_diff = (ts - last_1s_ts).total_seconds()
                    detected_tf = "1s" if time_diff <= 2 else "5m"
                else:
                    # Default to 5m for primary timeframe when no 1s data yet
                    detected_tf = "5m"
                    
            # Store data in correct timeframe key
            if detected_tf == "1s":
                data_dict["1s"] = pd.concat([data_dict["1s"], df])
                # Keep only recent 1s data to avoid memory issues
                if len(data_dict["1s"]) > 1000:
                    data_dict["1s"] = data_dict["1s"].tail(1000)
                
                # Track new 1s data
                if last_1s_data_ts is None or ts > last_1s_data_ts:
                    new_1s_data = True
                    last_1s_data_ts = ts
                    log.debug(f"📊 NEW 1s DATA: {ts}, price={row['close']:.6f}")
            elif detected_tf == "5m":
                # CRITICAL: Store 5m data in "5m" key for indicators
                if "5m" not in data_dict:
                    data_dict["5m"] = pd.DataFrame()
                    log.warning("⚠️ Creating missing 5m DataFrame in data_dict")
                
                data_dict["5m"] = pd.concat([data_dict["5m"], df])
                last_bar_ts = ts
                
                # Track new 5m data
                if last_5m_data_ts is None or ts > last_5m_data_ts:
                    new_5m_data = True
                    last_5m_data_ts = ts
                    log.info(f"📊 NEW 5m DATA: {ts}, price={row['close']:.6f}")
                    log.info(f"📊 5m DataFrame size: {len(data_dict['5m'])} rows")
            else:
                # Default: store in primary timeframe
                data_dict[timeframe] = pd.concat([data_dict[timeframe], df])
                last_bar_ts = ts
                
                # Track new data
                if detected_tf == timeframe:
                    if last_5m_data_ts is None or ts > last_5m_data_ts:
                        new_5m_data = True
                        last_5m_data_ts = ts
                        log.info(f"📊 NEW {timeframe} DATA: {ts}, price={row['close']:.6f}")

        elif typ == "trade":
            # Handle both CoinAPI and Binance timestamp formats
            timestamp_field = msg.get("time_exchange") or msg.get("time_trade") or msg.get("T")
            if isinstance(timestamp_field, (int, float)):
                # Binance millisecond timestamp
                ts = pd.to_datetime(timestamp_field, unit='ms', utc=True)
            else:
                ts = pd.to_datetime(timestamp_field, utc=True)

            try:
                price = float(msg.get("price", np.nan))
                qty   = float(msg.get("size") or msg.get("quantity") or msg.get("q") or np.nan)

                # Handle both CoinAPI and Binance trade side formats
                taker = msg.get("taker_side") or msg.get("side", "").upper()
                
                # Binance uses 'm' field for market maker flag
                if "m" in msg:
                    is_buyer_maker = not msg["m"]  # Binance: m=true means buyer is maker
                elif taker == "BUY":
                    is_buyer_maker = False      # agresor = BUY
                elif taker == "SELL":
                    is_buyer_maker = True       # agresor = SELL
                else:
                    is_buyer_maker = np.nan
            except (ValueError, TypeError) as e:
                log.warning(f"Failed to parse trade data from {source}: {e}")
                continue

            df = pd.DataFrame([{
                "price": price,
                "size" : qty,          # legacy názov
                "quantity": qty,       # čo chcú indikátory
                "is_buyer_maker": is_buyer_maker
            }], index=[ts])

            # Fix FutureWarning by checking if trades DataFrame is empty
            if data_dict["trades"].empty:
                data_dict["trades"] = df.tail(10_000)
            else:
                data_dict["trades"] = (
                    pd.concat([data_dict["trades"], df])
                      .tail(10_000)        # nedrž v pamäti zbytočne veľa
                )

        elif typ == "orderbook":
            bids, asks = msg.get("bids", []), msg.get("asks", [])
            
            # Handle timestamp from multiple sources
            timestamp_field = msg.get("time_exchange") or msg.get("time") or msg.get("T")
            if isinstance(timestamp_field, (int, float)):
                ts = pd.to_datetime(timestamp_field, unit='ms', utc=True)
            else:
                ts = pd.to_datetime(timestamp_field or pd.Timestamp.utcnow(), utc=True)

            def _parse_ob_entry(entry):

                px, qty = np.nan, np.nan
                try:
                    if isinstance(entry, dict):
                        px  = float(entry.get("price", np.nan))
                        qty = float(entry.get("size")  or entry.get("quantity", np.nan))
                    elif isinstance(entry, (list, tuple)) and len(entry) >= 2:
                        px  = float(entry[0])
                        qty = float(entry[1])
                except Exception:          # textový header „price/size" → NaN
                    pass
                return px, qty

            row = {}
            for lvl in range(1, 6):
                bid_entry = bids[lvl] if len(bids) > lvl else None   # lvl 0 = header
                ask_entry = asks[lvl] if len(asks) > lvl else None

                b_px, b_qty = _parse_ob_entry(bid_entry) if bid_entry else (np.nan, np.nan)
                a_px, a_qty = _parse_ob_entry(ask_entry) if ask_entry else (np.nan, np.nan)

                row[f"bid_px{lvl}"]  = b_px
                row[f"bid_qty{lvl}"] = b_qty
                row[f"ask_px{lvl}"]  = a_px
                row[f"ask_qty{lvl}"] = a_qty

            ob_df = pd.DataFrame([row], index=[ts])
            data_dict["orderbooks"] = ob_df

        # ●●●●●●●● 2) BASIC DATA VALIDATION ●●●●●●●●●●●●●●●●●●●●●●●●●
        if len(data_dict[timeframe]) < lookback:
            continue      # čakáme, kým nazbierame aspoň lookback barov

        # ●●●●●●●● SIMPLIFIED EVALUATION LOGIC ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
        # Simple time-based evaluation like simulate_trading_new.py
        now = pd.Timestamp.now(tz='UTC')
        time_since_last_eval = (now - last_evaluation_time).total_seconds()
        
        # Add basic throttling to prevent excessive evaluation
        if time_since_last_eval < min_evaluation_interval:
            continue
            
        # Update evaluation tracking
        last_evaluation_time = now
        current_ts = data_dict[timeframe].index[-1] if len(data_dict[timeframe]) > 0 else now
        last_decision_ts = current_ts
        
        log.info(f"🎯 EVALUATION: {time_since_last_eval:.1f}s since last, processing at {now}")

        # ●●●●●●●● 3) VÝPOČET INDIKÁTOROV (len keď robíme rozhodnutie) ●●●●●●●●●●●●●●●●●●●●●●●
        log.info("📊 Calculating features for decision...")
        base_df, calculated_features = calculate_and_merge_indicators(
            data_dict, cfg,
            skip_hmm = not cfg.get("hmmSettings", {}).get("enabled", False),
            hmm_model_external  = None,
            hmm_scaler_external = None
        )

        # Log what features were actually calculated
        log.info(f"📊 Features calculated by indicators: {len(calculated_features)} features")
        log.debug(f"📊 Calculated features: {calculated_features}")
        log.info(f"📊 Base DataFrame columns: {len(base_df.columns)} columns")
        log.debug(f"📊 Base DataFrame columns: {list(base_df.columns)}")
        
        # ●●●●●●●● FEATURE PROCESSING (exactly like simulate_trading_new.py) ●●●●●●●●●●●●●●●●●●●●●●●
        log.info("📊 Processing features for model compatibility...")
        
        # Get feature columns from config (exactly like simulate_trading_new.py)
        feature_cols = cfg["envSettings"]["feature_columns"]
        
        # Fill missing features with zeros (exactly like simulate_trading_new.py)
        missing_cols = [col for col in feature_cols if col not in base_df.columns]
        if missing_cols:
            log.warning(f"Missing {len(missing_cols)} columns → filled with zeros: {', '.join(missing_cols)}")
            base_df = base_df.copy()
            for col in missing_cols:
                base_df[col] = 0.0
        
        # Apply forward fill (exactly like simulate_trading_new.py)
        base_df = base_df.copy()
        base_df[feature_cols] = base_df[feature_cols].ffill()
        
        # Apply log1p transformation to volume columns (exactly like simulate_trading_new.py)
        vol_cols = [c for c in feature_cols if "vol" in c]
        if vol_cols:
            # Safe log1p transformation - handle zero/negative values
            for col in vol_cols:
                if col in base_df.columns:
                    # More robust NaN handling
                    base_df[col] = base_df[col].fillna(1e-8)
                    base_df[col] = np.where(base_df[col] <= 0, 1e-8, base_df[col])
            base_df[vol_cols] = np.log1p(base_df[vol_cols])
            log.debug(f"Applied log1p to volume columns: {vol_cols}")

        # Replace infinities with NaN (exactly like simulate_trading_new.py)
        base_df.replace([np.inf, -np.inf], np.nan, inplace=True)

        # ●●●●●●●● FEATURE SMOOTHING FOR GRADUAL UPDATES ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
        # Apply gradual feature updates when new 5m data arrives to prevent sudden changes
        if new_5m_data and previous_features is not None and len(base_df) > 0:
            # Get current features for the latest row
            current_row = base_df.iloc[-1].copy()

            # Apply smoothing to non-OHLCV features (keep price data immediate)
            ohlcv_fields = ["open", "high", "low", "close", "volume"]
            smoothed_features = []

            for col in feature_cols:
                if col in current_row.index and col in previous_features.index:
                    if col not in ohlcv_fields:  # Smooth indicators, not price data
                        # Apply EMA smoothing: new = alpha * current + (1-alpha) * previous
                        smoothed_value = (feature_smoothing_alpha * current_row[col] +
                                        (1 - feature_smoothing_alpha) * previous_features[col])
                        current_row[col] = smoothed_value
                        smoothed_features.append(col)

            # Update the DataFrame with smoothed values
            base_df.iloc[-1] = current_row

            if smoothed_features:
                log.info(f"📊 Applied feature smoothing to {len(smoothed_features)} indicators on new 5m data")
                log.debug(f"📊 Smoothed features: {smoothed_features[:5]}...")  # Log first 5

        # Store current features for next iteration
        if len(base_df) > 0:
            previous_features = base_df.iloc[-1].copy()
        
        # More selective NaN filtering - only check essential OHLCV features
        essential_features = [col for col in ['open', 'high', 'low', 'close', 'volume'] if col in feature_cols]
        if essential_features:
            # Check NaN status before filtering
            nan_counts = base_df[essential_features].isna().sum()
            if nan_counts.sum() > 0:
                log.warning(f"⚠️ NaN values in essential features before filtering: {nan_counts.to_dict()}")
            
            # Filter out rows with NaN in essential features only
            base_df = base_df[np.isfinite(base_df[essential_features]).all(axis=1)]
            log.info(f"✅ Filtered by essential features: {len(base_df)} rows remaining")
        
        # Fill remaining NaN values in non-essential features with 0
        remaining_features = [col for col in feature_cols if col not in essential_features]
        if remaining_features:
            nan_counts_before = base_df[remaining_features].isna().sum()
            if nan_counts_before.sum() > 0:
                log.info(f"📊 Filling NaN in non-essential features: {nan_counts_before[nan_counts_before > 0].to_dict()}")
                base_df[remaining_features] = base_df[remaining_features].fillna(0.0)
        
        # Extract only the required features in correct order (exactly like simulate_trading_new.py)
        base_df = base_df[feature_cols].astype("float32")
        
        # CRITICAL DIAGNOSTIC: Check data quality before final validation
        log.info(f"📊 Feature processing diagnostics:")
        log.info(f"   Total features: {len(feature_cols)}")
        log.info(f"   DataFrame shape: {base_df.shape}")
        log.info(f"   DataFrame columns: {len(base_df.columns)}")
        
        # Check for problematic values
        if len(base_df) > 0:
            inf_counts = np.isinf(base_df[feature_cols]).sum()
            nan_counts = base_df[feature_cols].isna().sum()
            
            if inf_counts.sum() > 0:
                log.warning(f"⚠️ Infinite values found: {inf_counts[inf_counts > 0].to_dict()}")
            if nan_counts.sum() > 0:
                log.warning(f"⚠️ NaN values found: {nan_counts[nan_counts > 0].to_dict()}")
            
            # Sample values from key features
            key_features = ['close', 'volume', 'RSI_14', 'EMA_21', 'ATR_14']
            for feat in key_features:
                if feat in base_df.columns:
                    val = base_df[feat].iloc[-1]
                    log.info(f"   {feat}: {val:.6f}")
        
        log.info(f"✅ Feature processing complete: {len(feature_cols)} features, {len(base_df)} rows")
        
        # CRITICAL: Ensure we have exactly the expected number of features
        if len(base_df.columns) != len(feature_cols):
            log.error(f"❌ FEATURE COUNT MISMATCH: Expected {len(feature_cols)} features, got {len(base_df.columns)}")
            log.error(f"❌ Expected features: {feature_cols}")
            log.error(f"❌ Actual features: {list(base_df.columns)}")
            
            # Find missing features
            missing_features = set(feature_cols) - set(base_df.columns)
            extra_features = set(base_df.columns) - set(feature_cols)
            
            if missing_features:
                log.error(f"❌ Missing features: {missing_features}")
                # Add missing features with default values
                for feat in missing_features:
                    base_df[feat] = 0.0
                    log.warning(f"⚠️ Added missing feature '{feat}' with default value 0.0")
            
            if extra_features:
                log.warning(f"⚠️ Extra features (will be ignored): {extra_features}")
            
            # Reorder columns to match expected order
            base_df = base_df[feature_cols]
            log.info(f"✅ Fixed feature count: {len(base_df.columns)} features")
        
        # Validate that we have data after processing
        if len(base_df) == 0:
            log.error("❌ No data remaining after feature processing - skipping evaluation")
            log.error("❌ This indicates all rows were filtered out due to NaN/infinite values")
            
            # CRITICAL DEBUG: Help identify the problem
            log.error("❌ Debugging suggestions:")
            log.error("   1. Check if 5m data is being updated properly")
            log.error("   2. Verify indicator calculations are not producing NaN")
            log.error("   3. Check if forward-fill is working correctly")
            continue
        
        # ●●●●●●●● INDICATOR CHANGE DETECTION ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
        # Calculate hash of key indicators to detect if they actually changed
        key_indicators = ['close', 'RSI_14', 'EMA_21', 'ATR_14', 'SMA_50', 'MACD_12_26_9']
        available_indicators = [col for col in key_indicators if col in base_df.columns]
        
        if len(base_df) > 0:
            # Get current indicator values
            current_values = []
            for col in available_indicators:
                val = base_df[col].iloc[-1]
                current_values.append(f"{col}:{val:.8f}")
            
            current_indicators_hash = hash(str(current_values))
            
            # Check if indicators actually changed
            indicators_changed = (last_indicators_hash is None or
                                current_indicators_hash != last_indicators_hash)
            
            if indicators_changed:
                log.info(f"✅ INDICATORS CHANGED: Hash {last_indicators_hash} → {current_indicators_hash}")
                # Log key indicator values
                for col in available_indicators[:4]:  # Log first 4 to avoid spam
                    val = base_df[col].iloc[-1]
                    log.info(f"   {col}: {val:.6f}")
                last_indicators_hash = current_indicators_hash
            else:
                log.warning(f"⚠️ INDICATORS UNCHANGED: Hash {current_indicators_hash}")
                log.warning(f"   This may indicate stale 5m data or calculation issues")
                # Still proceed with evaluation in case of time-based triggers
        
        # ●●●●●●●● FEATURE QUALITY DIAGNOSTICS ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
        if len(base_df) >= 5:
            # Check variance in recent data
            recent_data = base_df.tail(5)
            for col in available_indicators:
                if col in recent_data.columns:
                    variance = recent_data[col].var()
                    if variance < 1e-8:
                        log.warning(f"⚠️ LOW VARIANCE in {col}: {variance:.10f}")
                    else:
                        log.debug(f"✅ Good variance in {col}: {variance:.8f}")

        # ●●●●●●●● FORWARD-FILL LOGIC FOR 1s DECISIONS ●●●●●●●●●●●●●●●●●●●●●
        if use_1s_decisions:
            # Use current time for decision timestamp
            current_time = pd.Timestamp.now(tz='UTC')
            
            # Get the latest data for forward-fill
            if len(base_df) == 0:
                log.warning(f"⚠️ No base data available for forward-fill at {current_time}")
                continue
            
            # CRITICAL FIX: Preserve historical data for lookback window
            # Only update the latest row with current 1s OHLCV data
            base_df_copy = base_df.copy()
            
            # Get the most recent complete feature row
            latest_row = base_df_copy.iloc[-1].copy()
            latest_ts = base_df_copy.index[-1]
            
            # Update OHLCV with current 1s data if available
            if "1s" in data_dict and len(data_dict["1s"]) > 0:
                latest_1s_ohlcv = data_dict["1s"].iloc[-1]
                
                # Update only OHLCV fields, keep all other features unchanged
                ohlcv_fields = ["open", "high", "low", "close", "volume"]
                for field in ohlcv_fields:
                    if field in latest_row.index:
                        latest_row[field] = np.float32(latest_1s_ohlcv[field])
                
                log.debug(f"📊 Forward-fill with 1s OHLCV: price={latest_row['close']:.6f}")
            else:
                log.debug(f"📊 Forward-fill with 5m OHLCV: price={latest_row['close']:.6f}")
            
            # CRITICAL: Replace only the last row instead of entire DataFrame
            # This preserves the historical data needed for the lookback window
            base_df_copy.iloc[-1] = latest_row
            
            # Update the index of the last row to current time for decision
            base_df_copy.index = base_df_copy.index[:-1].tolist() + [current_time]
            
            # CRITICAL: Ensure all expected features are present
            for feat in feature_cols:
                if feat not in base_df_copy.columns:
                    base_df_copy[feat] = 0.0
                    log.warning(f"⚠️ Added missing feature '{feat}' during forward-fill")
            
            # Reorder columns to match expected order
            base_df_copy = base_df_copy[feature_cols]
            
            # Use the updated DataFrame with preserved historical data
            base_df = base_df_copy
            
            log.info(f"✅ Forward-fill complete: {len(base_df.columns)} features, {len(base_df)} rows")
            log.debug(f"📊 Using data from {latest_ts} for prediction at {current_time}")

        price_now = float(base_df["close"].iloc[-1])
        atr_cols  = base_df.filter(like="ATR_")
        atr_now   = float(atr_cols.iloc[-1].iat[0]) if not atr_cols.empty else 0.0

        # aktualizujeme trailing SL a soft BE
        te.check_trailing_stop(price_now, atr_now)
        te.check_soft_breakeven(price_now)
        te.update_equity(price_now)

        # ───────────── 3) PRÍPRAVA STAVU PRE AGENTA (exactly like simulate_trading_new.py) ──────────────────────
        # Get expected observation space from the actual dummy environment
        expected_obs = dummy_env.observation_space.shape[0] if hasattr(dummy_env, 'observation_space') else expected_obs_size
        lookback = cfg["envSettings"].get("state_lookback", 30)
        
        # Calculate expected features from observation space (subtract meta features)
        # Meta features: 7 trade features + 4 time features = 11 total
        feats_needed = (expected_obs - 11) // lookback if lookback > 0 else len(feature_cols)
        
        log.info(f"🔧 State vector dimensions:")
        log.info(f"   Expected total obs: {expected_obs}")
        log.info(f"   Lookback: {lookback}")
        log.info(f"   Features needed: {feats_needed}")
        log.info(f"   Features available: {len(feature_cols)}")
        log.info(f"   Meta features: 11 (7 trade + 4 time)")
        
        # CRITICAL FIX: Handle feature dimension mismatch exactly like simulate_trading_new.py
        if len(feature_cols) != feats_needed:
            log.warning(f"⚠️ FEATURE DIMENSION MISMATCH:")
            log.warning(f"   Model expects {feats_needed} features but config has {len(feature_cols)}")
            
            if len(feature_cols) > feats_needed:
                # Too many features - truncate to match model expectations
                log.warning(f"   Truncating {len(feature_cols) - feats_needed} features to match model")
                selected_features = feature_cols[:feats_needed]
                base_df = base_df[selected_features]
                log.info(f"   Using first {feats_needed} features: {selected_features[:3]}...{selected_features[-3:]}")
            else:
                # Too few features - pad with zeros
                log.warning(f"   Padding with {feats_needed - len(feature_cols)} zero features")
                missing_count = feats_needed - len(feature_cols)
                for i in range(missing_count):
                    base_df[f"pad_feature_{i}"] = 0.0
                selected_features = list(base_df.columns)
                log.info(f"   Padded to {len(selected_features)} features")
        else:
            selected_features = feature_cols
            log.info(f"   ✅ Feature count matches model expectations")

        # Extract features for state vector
        frame = base_df.to_numpy(dtype=np.float32)[-lookback:]
        
        # Ensure we have the right number of features for the model
        if frame.shape[1] != feats_needed:
            log.error(f"❌ Frame shape mismatch: expected {feats_needed}, got {frame.shape[1]}")
            continue
            
        log.debug(f"📊 Features extracted: {frame.shape[1]} features, lookback={lookback}")

        flat = frame.flatten()                       # Length = feats_needed * lookback
        
        # Clip frame data like in simulate_trading_new.py
        flat_clipped = np.clip(flat, -5.0, 5.0)
        
        # Verify the flat vector size matches expectations
        expected_flat_size = feats_needed * lookback
        if len(flat_clipped) != expected_flat_size:
            log.error(f"❌ Flat vector size mismatch: expected {expected_flat_size}, got {len(flat_clipped)}")
            continue
            
        log.debug(f"📊 Flat vector size: {len(flat_clipped)} (expected: {expected_flat_size})")

        pos, _, _ = te.get_current_position()
        
        # Vypočítajme dodatočné state features (ako v simulate_trading.py)
        _MIN_PRICE = 1e-6
        _PNL_NORM_DENOM = 0.01
        
        safe_current_price = max(price_now, _MIN_PRICE)
        safe_entry_price = max(te.entry_price if hasattr(te, 'entry_price') and te.entry_price else 0.0, _MIN_PRICE)
        
        # 1. PnL Normalizované
        pnl_norm = 0.0
        if pos != 0 and te.entry_price and te.entry_price > 0:
            pnl_norm = np.tanh(((safe_current_price - safe_entry_price) * pos) /
                               (safe_entry_price * _PNL_NORM_DENOM))
        
        # 2. Trvanie obchodu
        trade_duration = 0.0
        # Simplified for live trading
        
        # 3. Status profitu
        profit_status = 0.0
        if pos != 0 and te.entry_price and te.entry_price > 0:
            profit_status = np.sign((safe_current_price - safe_entry_price) * pos)
        
        # 4. & 5. Vzdialenosť od TP a SL
        tp_distance = 0.0
        sl_distance = 0.0
        # Simplified for live trading
        
        # 6. PnL / ATR Ratio
        pnl_atr_ratio = 0.0
        # Simplified for live trading
        
        # 7. Time features (ako v simulate_trading.py)
        current_ts = pd.Timestamp.now(tz='UTC') if use_1s_decisions else base_df.index[-1]
        cur_hour = current_ts.hour
        dow = current_ts.dayofweek
        
        time_feats = [
            np.sin(cur_hour / 24 * 2 * np.pi),
            np.cos(cur_hour / 24 * 2 * np.pi),
            np.sin(dow / 7 * 2 * np.pi),
            np.cos(dow / 7 * 2 * np.pi),
        ]
        
        # Spojenie všetkých častí (ako v simulate_trading.py)
        trade_features = [
            float(pos),
            pnl_norm,
            trade_duration,
            profit_status,
            tp_distance,
            sl_distance,
            pnl_atr_ratio
        ]
        
        state_vec = np.concatenate([
            flat_clipped,            # features * lookback (clipped like simulate_trading_new.py)
            trade_features,          # 7 prvkov
            time_feats              # 4 prvkov
        ]).astype(np.float32)       # Celkom features*lookback + 7 + 4

        # Log state vector info for debugging
        log.debug(f"State vector shape: {state_vec.shape}, expected: {expected_obs}")
        
        # Model prediction or test trade
        if test_trade_mode and not test_trade_executed:
            # Force a test LONG trade
            action = np.array([0.8, 0.0, 0.0, 0.0], dtype=np.float32)  # Strong LONG signal
            log.info(f"🧪 TEST TRADE: Forcing LONG action={action}, price=${price_now:.4f}, pos={pos}")
            log.info("🧪 Expected: Entry signal=0.8 > 0.5 threshold → LONG position with 2.0R TP")
            test_trade_executed = True
        else:
            # Normal model prediction
            if vecnorm:
                obs_normalized = vecnorm.normalize_obs(state_vec)
            else:
                obs_normalized = state_vec
                
            action, _ = model.predict(obs_normalized, deterministic=False)
            
            # DEBUG: Log detailed state vector info to identify why actions are identical
            # Calculate action variance to track model behavior
            if not hasattr(te, '_recent_actions'):
                te._recent_actions = []
            te._recent_actions.append(action.copy())
            if len(te._recent_actions) > 10:
                te._recent_actions = te._recent_actions[-10:]
            
            action_variance = np.var(te._recent_actions, axis=0) if len(te._recent_actions) > 1 else np.zeros(4)
            log.info(f"🤖 Model decision: action={action}, price=${price_now:.4f}, pos={pos}")
            log.info(f"🔍 Action variance (last 10): {action_variance}")
            log.info(f"🔍 State vector length: {len(state_vec)}, shape: {state_vec.shape}")
            log.info(f"🔍 Feature frame shape: {frame.shape}, last close: {base_df['close'].iloc[-1]:.6f}")
            log.info(f"🔍 Trade features: pos={pos}, pnl_norm={pnl_norm:.6f}")
            
            # CRITICAL DEBUG: Log raw state vector statistics
            state_mean = np.mean(state_vec)
            state_std = np.std(state_vec)
            state_min = np.min(state_vec)
            state_max = np.max(state_vec)
            log.info(f"🔍 State vector stats: mean={state_mean:.6f}, std={state_std:.6f}, min={state_min:.6f}, max={state_max:.6f}")
            
            # Log VecNormalize status
            if vecnorm:
                log.info(f"🔍 VecNormalize: training={vecnorm.training}, norm_obs={vecnorm.norm_obs}")
                obs_mean = np.mean(obs_normalized)
                obs_std = np.std(obs_normalized)
                log.info(f"🔍 Normalized obs stats: mean={obs_mean:.6f}, std={obs_std:.6f}")
            else:
                log.info("🔍 VecNormalize: NOT LOADED - using raw state vector")
            
            # Log a few key features to see if they're updating
            key_features = ['close', 'EMA_21', 'RSI_14', 'ATR_14']
            for feat in key_features:
                if feat in base_df.columns:
                    val = base_df[feat].iloc[-1]
                    log.debug(f"🔍 {feat}: {val:.6f}")
            
            # CRITICAL DEBUG: Check if we have feature variation in recent history
            if len(base_df) >= 5:
                recent_closes = base_df['close'].tail(5).values
                recent_rsi = base_df['RSI_14'].tail(5).values if 'RSI_14' in base_df.columns else []
                recent_ema = base_df['EMA_21'].tail(5).values if 'EMA_21' in base_df.columns else []
                recent_atr = base_df['ATR_14'].tail(5).values if 'ATR_14' in base_df.columns else []
                
                log.info(f"🔍 Last 5 closes: {recent_closes}")
                log.info(f"🔍 Last 5 RSI values: {recent_rsi}")
                log.info(f"🔍 Last 5 EMA values: {recent_ema}")
                log.info(f"🔍 Last 5 ATR values: {recent_atr}")
                
                # Check feature variance (CRITICAL DIAGNOSTIC)
                close_variance = np.var(recent_closes)
                rsi_variance = np.var(recent_rsi) if len(recent_rsi) > 0 else 0
                ema_variance = np.var(recent_ema) if len(recent_ema) > 0 else 0
                atr_variance = np.var(recent_atr) if len(recent_atr) > 0 else 0
                
                log.info(f"🔍 Feature variance - Close: {close_variance:.8f}, RSI: {rsi_variance:.8f}")
                log.info(f"🔍 Feature variance - EMA: {ema_variance:.8f}, ATR: {atr_variance:.8f}")
                
                # ALERT if features are not changing
                if close_variance < 1e-8 and rsi_variance < 1e-8:
                    log.warning("⚠️ FEATURES NOT UPDATING: Close and RSI showing no variance!")
                    log.warning("⚠️ This explains why model actions are identical!")
                    
            # Log current timestamp for debugging
            current_ts_debug = base_df.index[-1] if len(base_df) > 0 else "No data"
            log.info(f"🔍 Current timestamp: {current_ts_debug}")
            
            # Log decision frequency info
            if use_1s_decisions:
                log.info(f"🔍 1s mode: decision_ts={last_decision_ts}, current_ts={current_ts_debug}")
                if "1s" in data_dict and len(data_dict["1s"]) > 0:
                    latest_1s_ts = data_dict["1s"].index[-1]
                    log.info(f"🔍 Latest 1s data timestamp: {latest_1s_ts}")

        # ●●●●●●●● 5) EXECUTOR → Binance ●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●
        te.execute_with_binance(binance, action, price_now, atr_now)

# ──────────────────────────────────────────────────────────────────────────
if __name__ == "__main__":
    sys.exit(main())
