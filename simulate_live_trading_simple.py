#!/usr/bin/env python3
"""
Simple simulation of live_trading.py logic with historical data
This version focuses on the core trading logic without all the complexity
"""
import os, sys, json, logging, argparse
from pathlib import Path
from datetime import timedelta
import pandas as pd
import numpy as np
import torch

# Import our modules
from indicators import calculate_and_merge_indicators
from popart_sac import PopArtSAC
from agent import SimpleCNN1D, SafeReplayBuffer
from stable_baselines3.common.vec_env import VecNormalize, DummyVecEnv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(name)s] [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
    handlers=[logging.StreamHandler(sys.stdout)]
)
log = logging.getLogger("SimulateLiveTradingSimple")

def load_config(path: Path):
    """Load configuration from JSON file"""
    with open(path, 'r') as f:
        content = f.read()
        # Replace environment variables
        import re
        def replace_env_var(match):
            var_name = match.group(1)
            return os.environ.get(var_name, match.group(0))
        content = re.sub(r'\$\{([^}]+)\}', replace_env_var, content)
        cfg = json.loads(content)
    return cfg

def load_historical_data(symbol: str, start_date: str, end_date: str, timeframe: str = "1s"):
    """Load historical data from parquet files"""
    log.info(f"Loading historical data for {symbol} from {start_date} to {end_date}")
    
    # Convert dates
    start_dt = pd.to_datetime(start_date).tz_localize('UTC')
    end_dt = pd.to_datetime(end_date).tz_localize('UTC') + pd.Timedelta(days=1) - pd.Timedelta(seconds=1)
    
    # Load data from parquet files
    parquet_dir = Path(f"parquet_processed/{symbol}/{timeframe}")
    if not parquet_dir.exists():
        raise FileNotFoundError(f"Parquet directory not found: {parquet_dir}")
    
    # Find all parquet files in date range
    all_data = []
    current_date = start_dt.date()
    end_date_obj = end_dt.date()
    
    while current_date <= end_date_obj:
        parquet_file = parquet_dir / f"{current_date}.parquet"
        if parquet_file.exists():
            log.info(f"Loading: {parquet_file}")
            df = pd.read_parquet(parquet_file)
            
            # Check if timestamp is in column
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.set_index('timestamp')
            
            # Ensure index is datetime
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
            
            # Ensure timezone aware
            if df.index.tz is None:
                df.index = df.index.tz_localize('UTC')
            else:
                df.index = df.index.tz_convert('UTC')
            
            # Filter by time range
            df = df[(df.index >= start_dt) & (df.index <= end_dt)]
            if not df.empty:
                all_data.append(df)
        current_date += timedelta(days=1)
    
    if not all_data:
        raise ValueError(f"No data found for {symbol} in date range {start_date} to {end_date}")
    
    # Combine all data
    combined_df = pd.concat(all_data, axis=0).sort_index()
    log.info(f"Loaded {len(combined_df)} rows from {combined_df.index.min()} to {combined_df.index.max()}")
    
    return combined_df

class SimpleTradeExecutor:
    """Simplified trade executor for simulation"""
    def __init__(self, cfg):
        self.cfg = cfg
        self.pos = 0  # -1, 0, 1
        self.size = 0.0
        self.entry_price = 0.0
        self.balance = cfg["account"]["initialEquity"]
        self.trades = []
        
    def execute_decision(self, action, current_price, current_time):
        """Execute trading decision"""
        entry_sig = action[0]
        exit_sig = action[3] if len(action) > 3 else 0.0
        
        # Entry logic
        if self.pos == 0 and abs(entry_sig) > 0.5:  # Entry threshold
            direction = 1 if entry_sig > 0 else -1
            risk_amount = self.balance * 0.01  # 1% risk
            self.size = risk_amount / (current_price * 0.01)  # Assume 1% SL distance
            self.pos = direction
            self.entry_price = current_price
            
            log.info(f"📈 Entry: {direction} @ {current_price:.4f}, size: {self.size:.2f}")
        
        # Exit logic (simplified SL/TP)
        elif self.pos != 0:
            exit_triggered = False
            exit_reason = ""
            exit_price = current_price
            
            # Simple SL/TP logic
            if self.pos == 1:  # Long position
                if current_price <= self.entry_price * 0.99:  # 1% SL
                    exit_triggered = True
                    exit_reason = "SL"
                    exit_price = self.entry_price * 0.99
                elif current_price >= self.entry_price * 1.02:  # 2% TP
                    exit_triggered = True
                    exit_reason = "TP"
                    exit_price = self.entry_price * 1.02
            elif self.pos == -1:  # Short position
                if current_price >= self.entry_price * 1.01:  # 1% SL
                    exit_triggered = True
                    exit_reason = "SL"
                    exit_price = self.entry_price * 1.01
                elif current_price <= self.entry_price * 0.98:  # 2% TP
                    exit_triggered = True
                    exit_reason = "TP"
                    exit_price = self.entry_price * 0.98
            
            # Agent exit signal
            if not exit_triggered and abs(exit_sig) > 0.5:
                exit_triggered = True
                exit_reason = "AGENT"
                exit_price = current_price
            
            if exit_triggered:
                # Calculate PnL
                pnl = (exit_price - self.entry_price) * self.size * self.pos
                self.balance += pnl
                
                # Record trade
                trade = {
                    'timestamp': current_time,
                    'entry_price': self.entry_price,
                    'exit_price': exit_price,
                    'size': self.size,
                    'direction': self.pos,
                    'pnl': pnl,
                    'exit_reason': exit_reason
                }
                self.trades.append(trade)
                
                log.info(f"📉 Exit: {exit_reason} @ {exit_price:.4f}, PnL: ${pnl:.2f}")
                
                # Reset position
                self.pos = 0
                self.size = 0.0
                self.entry_price = 0.0

def main():
    """Main simulation function"""
    parser = argparse.ArgumentParser("Simple live trading simulation")
    parser.add_argument("--cfg", type=Path, default="strategyConfig_scalp_1s.json")
    parser.add_argument("--start", type=str, required=True, help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end", type=str, required=True, help="End date (YYYY-MM-DD)")
    parser.add_argument("--out-trades", type=str, help="Output file for trades CSV")
    parser.add_argument("--out-equity", type=str, help="Output file for equity curve CSV")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"])
    
    args = parser.parse_args()
    log.setLevel(args.log_level.upper())
    
    try:
        # Load configuration
        cfg = load_config(args.cfg)
        symbol = cfg.get("symbol") or cfg.get("dataProvider", {}).get("symbol", "XRPUSDC")
        
        log.info(f"🚀 Starting simulation for {symbol} from {args.start} to {args.end}")
        
        # Load historical data
        historical_1s = load_historical_data(symbol, args.start, args.end, "1s")
        
        # Create 5m data by resampling
        historical_5m = historical_1s.resample('5min').agg({
            'open': 'first', 'high': 'max', 'low': 'min', 'close': 'last', 'volume': 'sum'
        }).dropna()
        
        log.info(f"📊 Data loaded: {len(historical_1s)} 1s bars, {len(historical_5m)} 5m bars")
        
        # Calculate indicators
        log.info("📊 Calculating indicators...")
        data_dict = {'5m': historical_5m, '1s': historical_1s}
        base_df, _ = calculate_and_merge_indicators(data_dict, cfg, skip_hmm=True)
        
        log.info(f"📊 Indicators calculated: {base_df.shape}")
        
        # Load model
        log.info("🤖 Loading model...")
        model_files = list(Path(".").glob("sac_*_steps.zip"))
        if not model_files:
            raise FileNotFoundError("No SAC model files found!")

        latest_model = sorted(model_files, key=lambda x: int(x.stem.split('_')[1]))[-1]
        log.info(f"Using model: {latest_model}")

        # Setup model loading
        feature_cols = cfg["envSettings"]["feature_columns"]
        lookback = cfg["envSettings"].get("state_lookback", 30)
        expected_obs_size = len(feature_cols) * lookback + 11

        device = "cuda" if torch.cuda.is_available() else "cpu"
        training_settings = cfg["trainingSettings"]

        policy_kwargs = {
            "net_arch": training_settings["netArch"],
            "features_extractor_class": SimpleCNN1D,
            "features_extractor_kwargs": {
                **training_settings["featureExtractorKwargs"],
                "feature_names": feature_cols,
                "meta_len": 11
            }
        }

        custom_objects = {
            "buffer_size": training_settings["bufferSize"],
            "policy_kwargs": policy_kwargs,
            "replay_buffer_class": SafeReplayBuffer,
            "learning_rate": 0.0001
        }

        model = PopArtSAC.load(latest_model, device=device, custom_objects=custom_objects)
        log.info(f"✅ Model loaded: {model.observation_space.shape}")

        # Initialize trade executor
        te = SimpleTradeExecutor(cfg)

        # Prepare data for simulation
        log.info("📊 Preparing data for simulation...")

        # Extract only required features
        missing_cols = [col for col in feature_cols if col not in base_df.columns]
        if missing_cols:
            log.warning(f"Missing {len(missing_cols)} columns → filled with zeros")
            for col in missing_cols:
                base_df[col] = 0.0

        # Apply forward fill and select features
        base_df = base_df[feature_cols].ffill().fillna(0).astype("float32")

        log.info(f"📊 Data prepared: {base_df.shape}")

        # Run simulation
        log.info("🔄 Starting simulation...")
        equity_curve = []

        start_idx = lookback
        total_steps = len(base_df) - start_idx

        for i in range(start_idx, len(base_df)):
            current_time = base_df.index[i]
            current_price = base_df.iloc[i]['close'] if 'close' in base_df.columns else base_df.iloc[i][3]

            # Extract features for state vector
            frame = base_df.to_numpy(dtype=np.float32)[i-lookback:i]

            # Create meta features
            pos = te.pos
            size = te.size
            entry_price = te.entry_price if te.entry_price > 0 else current_price

            # Trade meta features (7)
            trade_meta = np.array([
                pos, size, entry_price, 0.0, 0.0, 0.0, 0.0
            ], dtype=np.float32)

            # Time meta features (4)
            hour = current_time.hour / 23.0
            minute = current_time.minute / 59.0
            day_of_week = current_time.weekday() / 6.0
            day_of_month = current_time.day / 31.0
            time_meta = np.array([hour, minute, day_of_week, day_of_month], dtype=np.float32)

            # Combine all features
            flattened_frame = frame.flatten()
            state = np.concatenate([flattened_frame, trade_meta, time_meta])

            # Get model prediction
            action, _ = model.predict(state, deterministic=True)

            # Execute trading decision
            te.execute_decision(action, current_price, current_time)

            # Track equity
            unrealized_pnl = 0.0
            if te.pos != 0 and te.entry_price > 0:
                unrealized_pnl = (current_price - te.entry_price) * te.size * te.pos

            current_equity = te.balance + unrealized_pnl
            equity_curve.append({
                'timestamp': current_time,
                'equity': current_equity,
                'balance': te.balance,
                'position': te.pos,
                'unrealized_pnl': unrealized_pnl
            })

            # Progress logging
            if i % 1000 == 0:
                progress = (i - start_idx) / total_steps * 100
                log.info(f"📊 Progress: {progress:.1f}% - Equity: ${current_equity:.2f}, Trades: {len(te.trades)}")

        log.info("✅ Simulation completed!")

        # Save results
        if args.out_trades and te.trades:
            trades_df = pd.DataFrame(te.trades)
            trades_df.to_csv(args.out_trades, index=False)
            log.info(f"💾 Trades saved to: {args.out_trades}")

        if args.out_equity and equity_curve:
            equity_df = pd.DataFrame(equity_curve)
            equity_df.set_index('timestamp', inplace=True)
            equity_df.to_csv(args.out_equity)
            log.info(f"💾 Equity curve saved to: {args.out_equity}")

        # Print summary
        total_pnl = sum(trade['pnl'] for trade in te.trades)
        winning_trades = sum(1 for trade in te.trades if trade['pnl'] > 0)
        win_rate = winning_trades / len(te.trades) * 100 if te.trades else 0

        log.info("=" * 60)
        log.info("🏁 SIMULATION RESULTS")
        log.info("=" * 60)
        log.info(f"📊 Total trades: {len(te.trades)}")
        log.info(f"💰 Final equity: ${te.balance:.2f}")
        log.info(f"📈 Total PnL: ${total_pnl:.2f}")
        log.info(f"🎯 Win rate: {win_rate:.1f}%")
        log.info(f"📊 Return: {((te.balance / cfg['account']['initialEquity']) - 1) * 100:.2f}%")
        
    except Exception as e:
        log.error(f"❌ Simulation failed: {e}")
        import traceback
        log.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
