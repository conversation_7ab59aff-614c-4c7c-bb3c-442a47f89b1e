2025-07-10 01:45:41,591 INFO  Loading config from: strategyConfig_scalp_1s.json
2025-07-10 01:45:41,591 INFO  🔍 DEBUG: Using CoinAPI key: a49bb33b...1cef
2025-07-10 01:45:41,591 INFO  Config loaded for symbol: XRPUSDC
2025-07-10 01:45:41,591 INFO  🚀 Using 1s decision frequency with 5m features (forward-filled)
2025-07-10 01:45:42,256 INFO  🚀 Setting up Binance Futures trading...
2025-07-10 01:45:43,185 INFO  ✅ Set leverage to 10x for XRPUSDC
2025-07-10 01:45:43,481 WARNING Failed to set margin type: APIError(code=-4175): Cannot change to ISOLATED mode due to credit status.
2025-07-10 01:45:43,481 WARNING ⚠️ Account credit/balance issue - continuing with default margin mode
2025-07-10 01:45:43,481 WARNING    You may need to verify account status or add more balance
2025-07-10 01:45:43,481 INFO  🔄 Symbol mapping: Config='XRPUSDC' → CoinAPI='BINANCEFTS_PERP_XRP_USDC'
2025-07-10 01:45:43,481 INFO  DataProvider initialized for XRPUSDC/1s
2025-07-10 01:45:43,482 INFO  Data directory: parquet_processed
2025-07-10 01:45:43,482 INFO  TradeExecutor initialized:
2025-07-10 01:45:43,482 INFO    Entry thresholds - Long: 0.7, Short: 0.7, Exit: 0.7
2025-07-10 01:45:43,482 INFO    Risk-Reward Target: 2.5
2025-07-10 01:45:43,482 INFO    Agent Exits Enabled: False
2025-07-10 01:45:43,482 INFO    TSL Enabled: True (Activate: 0.2R, Trail: 0.35x ATR)
2025-07-10 01:45:43,482 INFO    Initial Equity: $100.00
2025-07-10 01:45:43,482 INFO  🔄 Configuration loaded:
2025-07-10 01:45:43,482 INFO     Symbol: XRPUSDC
2025-07-10 01:45:43,482 INFO     Primary Timeframe: 1s
2025-07-10 01:45:43,482 INFO     Use 1s decisions: True
2025-07-10 01:45:43,482 INFO     Test mode: False
2025-07-10 01:45:43,482 INFO  💰 TradeExecutor initialized in LIVE MODE - real orders will be placed!
2025-07-10 01:45:43,482 INFO  Using model: sac_9996800_steps.zip
2025-07-10 01:45:43,483 INFO  Using vecnorm: sac_9996800.vecnorm.pkl
2025-07-10 01:45:43,483 INFO  🔧 Environment setup:
2025-07-10 01:45:43,483 INFO     Feature columns: 48 features
2025-07-10 01:45:43,483 INFO     Lookback: 30
2025-07-10 01:45:43,483 INFO     Expected observation space: 1451
2025-07-10 01:45:43,483 INFO     Features sample: ['open', 'high', 'low', 'close', 'volume']...
2025-07-10 01:45:43,491 INFO  ✅ VecNormalize loaded successfully
2025-07-10 01:45:43,491 INFO  📦 Loading final PopArt SAC model...
2025-07-10 01:45:44,112 INFO  ✅ PopArt SAC model loaded successfully from sac_9996800_steps.zip
2025-07-10 01:45:44,112 INFO     Model observation space: (1451,)
2025-07-10 01:45:44,112 INFO     Using 48 features for compatibility
2025-07-10 01:45:44,112 INFO  🔄 Starting Fallback Data Provider (CoinAPI + Binance fallback)...
2025-07-10 01:45:44,112 INFO  🔍 DEBUG: FallbackDataProvider using CoinAPI key: a49bb33b...1cef
2025-07-10 01:45:44,112 INFO  FallbackDataProvider initialized:
2025-07-10 01:45:44,112 INFO    Original symbol: XRPUSDC
2025-07-10 01:45:44,112 INFO    Binance symbol: xrpusdc
2025-07-10 01:45:44,112 INFO    CoinAPI symbol: BINANCEFTS_PERP_XRP_USDC
2025-07-10 01:45:44,112 INFO  🔧 Added timeframe '5m' for indicator 'hmm_5m'
2025-07-10 01:45:44,113 INFO  💎 Starting CoinAPI-ONLY data streams for timeframes: ['5m', '1s']
2025-07-10 01:45:44,113 INFO  🔄 Fetching initial 5m data for indicators...
2025-07-10 01:45:44,113 INFO  🔄 Fetching initial 5m data from CoinAPI REST API...
2025-07-10 01:45:44,113 INFO  🔍 Fetching 5m data from 2025-07-08T22:45:44.000Z to 2025-07-09T23:45:44.000Z
2025-07-10 01:45:44,832 INFO  ✅ Fetched 300 initial 5m bars from CoinAPI REST
2025-07-10 01:45:44,834 INFO  ✅ Initial 5m data loaded successfully
2025-07-10 01:45:44,835 INFO  🚀 Starting CoinAPI stream for 5m timeframe
2025-07-10 01:45:44,836 INFO  🚀 Starting Binance WebSocket thread for 5m
2025-07-10 01:45:44,836 INFO  🚀 Starting CoinAPI stream for 1s timeframe
2025-07-10 01:45:44,836 INFO  🔍 Connecting to CoinAPI WebSocket for 5m...
2025-07-10 01:45:44,837 INFO  🚀 Starting Binance WebSocket thread for 1s
2025-07-10 01:45:44,837 INFO  🔍 Connecting to CoinAPI WebSocket for 1s...
2025-07-10 01:45:44,837 INFO  🔍 CoinAPI URL: wss://ws.coinapi.io/v1/
2025-07-10 01:45:44,838 INFO  🔍 CoinAPI URL: wss://ws.coinapi.io/v1/
2025-07-10 01:45:44,838 INFO  🔍 CoinAPI Symbol: BINANCEFTS_PERP_XRP_USDC
2025-07-10 01:45:44,838 INFO  🔍 CoinAPI Symbol: BINANCEFTS_PERP_XRP_USDC
2025-07-10 01:45:45,070 INFO  🔍 CoinAPI 5m subscription with period_id: 5MIN
2025-07-10 01:45:45,071 INFO  🔍 CoinAPI subscription message: {
  "type": "hello",
  "apikey": "a49bb33b-6504-4776-acc2-25c1026b1cef",
  "heartbeat": true,
  "subscribe_data_type": [
    "trade",
    "book"
  ],
  "subscribe_filter_symbol_id": [
    "BINANCEFTS_PERP_XRP_USDC"
  ],
  "subscribe_data_format": "JSON"
}
2025-07-10 01:45:45,071 INFO  🔍 CoinAPI subscription message: {
  "type": "hello",
  "apikey": "a49bb33b-6504-4776-acc2-25c1026b1cef",
  "heartbeat": true,
  "subscribe_data_type": [
    "ohlcv"
  ],
  "subscribe_filter_symbol_id": [
    "BINANCEFTS_PERP_XRP_USDC"
  ],
  "subscribe_filter_period_id": [
    "5MIN"
  ],
  "subscribe_data_format": "JSON"
}
2025-07-10 01:45:45,072 INFO  ✅ CoinAPI 1s subscription sent
2025-07-10 01:45:45,072 INFO  ✅ CoinAPI 5m subscription sent
2025-07-10 01:45:45,117 INFO  📥 CoinAPI 1s message #1: {"type":"hearbeat"}...
2025-07-10 01:45:45,118 INFO  📥 CoinAPI 5m message #1: {"type":"hearbeat"}...
2025-07-10 01:45:45,118 INFO  📊 CoinAPI 1s parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:45,118 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:45,241 INFO  📥 CoinAPI 1s message #2: {"is_snapshot":true,"time_exchange":"2025-07-09T23:45:44.7810000Z","time_coinapi":"2025-07-09T23:45:44.9461209Z","asks":[{"price":2.4061,"size":4241.2},{"price":2.4062,"size":4299.5},{"price":2.4063,"...
2025-07-10 01:45:45,248 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': True, 'time_exchange': '2025-07-09T23:45:44.7810000Z', 'time_coinapi': '2025-07-09T23:45:44.9461209Z', 'asks': [{'price': 2.4061, 'size': 4241.2}, {'price': 2.4062, 'size': 4299.5}, {'...
2025-07-10 01:45:45,248 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:45,248 INFO  📥 CoinAPI 1s message #3: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:44.7810000Z","time_coinapi":"2025-07-09T23:45:44.9461209Z","asks":[{"price":2.4059,"size":0},{"price":2.4060,"size":0},{"price":2.4061,"size":424...
2025-07-10 01:45:45,248 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:44.7810000Z', 'time_coinapi': '2025-07-09T23:45:44.9461209Z', 'asks': [{'price': 2.4059, 'size': 0}, {'price': 2.406, 'size': 0}, {'price': 2....
2025-07-10 01:45:45,248 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:46,115 INFO  📥 CoinAPI 5m message #2: {"type":"hearbeat"}...
2025-07-10 01:45:46,116 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:46,159 INFO  📥 CoinAPI 1s message #4: {"time_exchange":"2025-07-09T23:45:44.7140000Z","time_coinapi":"2025-07-09T23:45:44.8774907Z","uuid":"af7b347e-0e75-4a09-b97c-eed82d4846ed","price":2.4059,"size":2.1,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:45:46,159 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:45:44.7140000Z', 'time_coinapi': '2025-07-09T23:45:44.8774907Z', 'uuid': 'af7b347e-0e75-4a09-b97c-eed82d4846ed', 'price': 2.4059, 'size': 2.1, 'taker_side': 'BUY', 'sy...
2025-07-10 01:45:46,160 INFO  📊 CoinAPI trade received for 1s: price=2.4059, size=2.1
2025-07-10 01:45:46,160 INFO  🆕 Created new 5m bar starting at 2025-07-09 23:45:00+00:00
2025-07-10 01:45:46,160 INFO  📥 CoinAPI 1s message #5: {"time_exchange":"2025-07-09T23:45:44.7140000Z","time_coinapi":"2025-07-09T23:45:44.8775618Z","uuid":"a4e37460-ec57-4d2c-bebe-dd75d319e119","price":2.4059,"size":98.8,"taker_side":"BUY","symbol_id":"B...
2025-07-10 01:45:46,160 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:45:44.7140000Z', 'time_coinapi': '2025-07-09T23:45:44.8775618Z', 'uuid': 'a4e37460-ec57-4d2c-bebe-dd75d319e119', 'price': 2.4059, 'size': 98.8, 'taker_side': 'BUY', 's...
2025-07-10 01:45:46,160 INFO  📊 CoinAPI trade received for 1s: price=2.4059, size=98.8
2025-07-10 01:45:46,160 INFO  📥 CoinAPI 1s message #6: {"time_exchange":"2025-07-09T23:45:44.7140000Z","time_coinapi":"2025-07-09T23:45:44.8775759Z","uuid":"9db967ae-d0d5-4b67-b2cc-e1926024edf5","price":2.4059,"size":50.0,"taker_side":"BUY","symbol_id":"B...
2025-07-10 01:45:46,160 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:45:44.7140000Z', 'time_coinapi': '2025-07-09T23:45:44.8775759Z', 'uuid': '9db967ae-d0d5-4b67-b2cc-e1926024edf5', 'price': 2.4059, 'size': 50.0, 'taker_side': 'BUY', 's...
2025-07-10 01:45:46,160 INFO  📊 CoinAPI trade received for 1s: price=2.4059, size=50.0
2025-07-10 01:45:46,160 INFO  📥 CoinAPI 1s message #7: {"time_exchange":"2025-07-09T23:45:44.7140000Z","time_coinapi":"2025-07-09T23:45:44.8775885Z","uuid":"bb86cc11-22e4-4cbe-8227-fae65ad13a90","price":2.4059,"size":6.9,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:45:46,160 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:45:44.7140000Z', 'time_coinapi': '2025-07-09T23:45:44.8775885Z', 'uuid': 'bb86cc11-22e4-4cbe-8227-fae65ad13a90', 'price': 2.4059, 'size': 6.9, 'taker_side': 'BUY', 'sy...
2025-07-10 01:45:46,160 INFO  📊 CoinAPI trade received for 1s: price=2.4059, size=6.9
2025-07-10 01:45:46,270 INFO  📥 CoinAPI 1s message #8: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:46.0910000Z","time_coinapi":"2025-07-09T23:45:46.2543799Z","asks":[{"price":2.4061,"size":5751.7},{"price":2.4062,"size":3917.5},{"price":2.4063,...
2025-07-10 01:45:46,272 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:46.0910000Z', 'time_coinapi': '2025-07-09T23:45:46.2543799Z', 'asks': [{'price': 2.4061, 'size': 5751.7}, {'price': 2.4062, 'size': 3917.5}, {...
2025-07-10 01:45:46,272 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:46,673 INFO  📥 CoinAPI 1s message #9: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:46.4990000Z","time_coinapi":"2025-07-09T23:45:46.6615171Z","asks":[{"price":2.4078,"size":14239.0},{"price":2.4079,"size":11478.4},{"price":2.410...
2025-07-10 01:45:46,675 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:46.4990000Z', 'time_coinapi': '2025-07-09T23:45:46.6615171Z', 'asks': [{'price': 2.4078, 'size': 14239.0}, {'price': 2.4079, 'size': 11478.4},...
2025-07-10 01:45:46,675 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:47,112 INFO  📥 CoinAPI 5m message #3: {"type":"hearbeat"}...
2025-07-10 01:45:47,113 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:47,191 INFO  📥 CoinAPI 1s message #10: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:47.0060000Z","time_coinapi":"2025-07-09T23:45:47.1691510Z","asks":[{"price":2.4061,"size":5333.6},{"price":2.4064,"size":4743.1},{"price":2.4065,...
2025-07-10 01:45:47,191 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:47.0060000Z', 'time_coinapi': '2025-07-09T23:45:47.1691510Z', 'asks': [{'price': 2.4061, 'size': 5333.6}, {'price': 2.4064, 'size': 4743.1}, {...
2025-07-10 01:45:47,192 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:47,288 INFO  📥 CoinAPI 1s message #11: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:47.1150000Z","time_coinapi":"2025-07-09T23:45:47.2787372Z","asks":[{"price":2.4075,"size":13202.5}],"bids":[],"symbol_id":"BINANCEFTS_PERP_XRP_US...
2025-07-10 01:45:47,288 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:47.1150000Z', 'time_coinapi': '2025-07-09T23:45:47.2787372Z', 'asks': [{'price': 2.4075, 'size': 13202.5}], 'bids': [], 'symbol_id': 'BINANCEF...
2025-07-10 01:45:47,288 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:47,723 INFO  📥 CoinAPI 1s message #12: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:47.5440000Z","time_coinapi":"2025-07-09T23:45:47.7108815Z","asks":[{"price":2.4061,"size":3841.1},{"price":2.4062,"size":2073.5},{"price":2.4074,...
2025-07-10 01:45:47,724 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:47.5440000Z', 'time_coinapi': '2025-07-09T23:45:47.7108815Z', 'asks': [{'price': 2.4061, 'size': 3841.1}, {'price': 2.4062, 'size': 2073.5}, {...
2025-07-10 01:45:47,724 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:47,821 INFO  📥 CoinAPI 1s message #13: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:47.6470000Z","time_coinapi":"2025-07-09T23:45:47.8136536Z","asks":[{"price":2.4062,"size":3566.0},{"price":2.4105,"size":6278.1}],"bids":[{"price...
2025-07-10 01:45:47,822 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:47.6470000Z', 'time_coinapi': '2025-07-09T23:45:47.8136536Z', 'asks': [{'price': 2.4062, 'size': 3566.0}, {'price': 2.4105, 'size': 6278.1}], ...
2025-07-10 01:45:47,822 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:47,839 INFO  ✅ All CoinAPI streams started
2025-07-10 01:45:47,839 INFO  ✅ Data streams started for timeframes: ['5m', '1s']
2025-07-10 01:45:47,839 INFO  🔄 Loading historical data from parquet files before starting WebSocket...
2025-07-10 01:45:47,976 INFO  📥 CoinAPI 1s message #14: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:47.7480000Z","time_coinapi":"2025-07-09T23:45:47.9133234Z","asks":[{"price":2.4077,"size":6065.4},{"price":2.4078,"size":3401.0},{"price":2.4104,...
2025-07-10 01:45:47,977 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:47.7480000Z', 'time_coinapi': '2025-07-09T23:45:47.9133234Z', 'asks': [{'price': 2.4077, 'size': 6065.4}, {'price': 2.4078, 'size': 3401.0}, {...
2025-07-10 01:45:47,977 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:48,012 WARNING Missing data file: parquet_processed/XRPUSDC/1s/2025-07-07.parquet
2025-07-10 01:45:48,012 WARNING Missing data file: parquet_processed/XRPUSDC/1s/2025-07-08.parquet
2025-07-10 01:45:48,012 WARNING Missing data file: parquet_processed/XRPUSDC/1s/2025-07-09.parquet
2025-07-10 01:45:48,019 INFO  Loaded 56223 historical records from 2025-07-03 23:45:47.839975+00:00 to 2025-07-09 23:45:47.839975+00:00
2025-07-10 01:45:48,019 INFO  ✅ Loaded 56223 historical 1s bars from parquet files
2025-07-10 01:45:48,045 INFO  📥 CoinAPI 1s message #15: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:47.8860000Z","time_coinapi":"2025-07-09T23:45:48.0487811Z","asks":[{"price":2.4064,"size":5381.2},{"price":2.4066,"size":11893.1},{"price":2.4078...
2025-07-10 01:45:48,045 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:47.8860000Z', 'time_coinapi': '2025-07-09T23:45:48.0487811Z', 'asks': [{'price': 2.4064, 'size': 5381.2}, {'price': 2.4066, 'size': 11893.1}, ...
2025-07-10 01:45:48,045 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:48,051 WARNING Missing data file: parquet_processed/XRPUSDC/1s/2025-07-07.parquet
2025-07-10 01:45:48,051 WARNING Missing data file: parquet_processed/XRPUSDC/1s/2025-07-08.parquet
2025-07-10 01:45:48,051 WARNING Missing data file: parquet_processed/XRPUSDC/1s/2025-07-09.parquet
2025-07-10 01:45:48,058 INFO  Loaded 56223 historical records from 2025-07-03 23:45:47.839975+00:00 to 2025-07-09 23:45:47.839975+00:00
2025-07-10 01:45:48,058 INFO  ✅ Loaded 56223 historical 1s bars from parquet files
2025-07-10 01:45:48,058 INFO  ℹ️ 1s decision mode: will forward-fill 5m features to 1s resolution
2025-07-10 01:45:48,058 INFO  🔄 Waiting for initial 5m data to load...
2025-07-10 01:45:48,111 INFO  📥 CoinAPI 5m message #4: {"type":"hearbeat"}...
2025-07-10 01:45:48,112 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:48,150 INFO  📥 CoinAPI 1s message #16: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:47.9870000Z","time_coinapi":"2025-07-09T23:45:48.1499959Z","asks":[{"price":2.4066,"size":12938.2},{"price":2.4067,"size":13378.0},{"price":2.410...
2025-07-10 01:45:48,151 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:47.9870000Z', 'time_coinapi': '2025-07-09T23:45:48.1499959Z', 'asks': [{'price': 2.4066, 'size': 12938.2}, {'price': 2.4067, 'size': 13378.0},...
2025-07-10 01:45:48,151 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:48,347 INFO  📥 CoinAPI 1s message #17: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:48.1870000Z","time_coinapi":"2025-07-09T23:45:48.3525589Z","asks":[{"price":2.4064,"size":4743.1},{"price":2.4073,"size":20885.2},{"price":2.4074...
2025-07-10 01:45:48,348 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:48.1870000Z', 'time_coinapi': '2025-07-09T23:45:48.3525589Z', 'asks': [{'price': 2.4064, 'size': 4743.1}, {'price': 2.4073, 'size': 20885.2}, ...
2025-07-10 01:45:48,348 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:49,082 INFO  📥 CoinAPI 1s message #18: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:48.8180000Z","time_coinapi":"2025-07-09T23:45:48.9801046Z","asks":[{"price":2.4061,"size":1688.8},{"price":2.4062,"size":2073.5},{"price":2.4063,...
2025-07-10 01:45:49,083 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:48.8180000Z', 'time_coinapi': '2025-07-09T23:45:48.9801046Z', 'asks': [{'price': 2.4061, 'size': 1688.8}, {'price': 2.4062, 'size': 2073.5}, {...
2025-07-10 01:45:49,083 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:49,113 INFO  📥 CoinAPI 5m message #5: {"type":"hearbeat"}...
2025-07-10 01:45:49,114 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:49,978 INFO  📥 CoinAPI 1s message #19: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:49.7950000Z","time_coinapi":"2025-07-09T23:45:49.9661246Z","asks":[{"price":2.4061,"size":0},{"price":2.4062,"size":9435.9},{"price":2.4063,"size...
2025-07-10 01:45:49,979 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:49.7950000Z', 'time_coinapi': '2025-07-09T23:45:49.9661246Z', 'asks': [{'price': 2.4061, 'size': 0}, {'price': 2.4062, 'size': 9435.9}, {'pric...
2025-07-10 01:45:49,979 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:50,083 INFO  📥 CoinAPI 1s message #20: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:49.9060000Z","time_coinapi":"2025-07-09T23:45:50.0756219Z","asks":[{"price":2.4063,"size":2464.0}],"bids":[{"price":2.4061,"size":5424.8},{"price...
2025-07-10 01:45:50,083 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:49.9060000Z', 'time_coinapi': '2025-07-09T23:45:50.0756219Z', 'asks': [{'price': 2.4063, 'size': 2464.0}], 'bids': [{'price': 2.4061, 'size': ...
2025-07-10 01:45:50,083 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:50,119 INFO  📥 CoinAPI 5m message #6: {"type":"hearbeat"}...
2025-07-10 01:45:50,120 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:50,191 INFO  📥 CoinAPI 1s message #21: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:50.0090000Z","time_coinapi":"2025-07-09T23:45:50.1725737Z","asks":[{"price":2.4064,"size":4747.7},{"price":2.4068,"size":22353.1},{"price":2.4070...
2025-07-10 01:45:50,191 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:50.0090000Z', 'time_coinapi': '2025-07-09T23:45:50.1725737Z', 'asks': [{'price': 2.4064, 'size': 4747.7}, {'price': 2.4068, 'size': 22353.1}, ...
2025-07-10 01:45:50,192 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:50,282 INFO  📥 CoinAPI 1s message #22: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:50.1250000Z","time_coinapi":"2025-07-09T23:45:50.2873293Z","asks":[{"price":2.4063,"size":3956.5},{"price":2.4073,"size":11808.7}],"bids":[{"pric...
2025-07-10 01:45:50,283 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:50.1250000Z', 'time_coinapi': '2025-07-09T23:45:50.2873293Z', 'asks': [{'price': 2.4063, 'size': 3956.5}, {'price': 2.4073, 'size': 11808.7}],...
2025-07-10 01:45:50,283 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:50,447 INFO  📥 CoinAPI 1s message #23: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:50.2900000Z","time_coinapi":"2025-07-09T23:45:50.4528009Z","asks":[{"price":2.4082,"size":18991.2},{"price":2.4106,"size":62432.3},{"price":2.410...
2025-07-10 01:45:50,448 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:50.2900000Z', 'time_coinapi': '2025-07-09T23:45:50.4528009Z', 'asks': [{'price': 2.4082, 'size': 18991.2}, {'price': 2.4106, 'size': 62432.3},...
2025-07-10 01:45:50,448 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:50,558 INFO  📥 CoinAPI 1s message #24: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:50.4020000Z","time_coinapi":"2025-07-09T23:45:50.5649947Z","asks":[],"bids":[{"price":2.4061,"size":5923.4},{"price":1.9249,"size":11.8}],"symbol...
2025-07-10 01:45:50,558 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:50.4020000Z', 'time_coinapi': '2025-07-09T23:45:50.5649947Z', 'asks': [], 'bids': [{'price': 2.4061, 'size': 5923.4}, {'price': 1.9249, 'size'...
2025-07-10 01:45:50,558 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:50,849 INFO  📥 CoinAPI 1s message #25: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:50.6720000Z","time_coinapi":"2025-07-09T23:45:50.8349771Z","asks":[{"price":2.4075,"size":14366.2},{"price":2.4081,"size":13252.9},{"price":2.410...
2025-07-10 01:45:50,849 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:50.6720000Z', 'time_coinapi': '2025-07-09T23:45:50.8349771Z', 'asks': [{'price': 2.4075, 'size': 14366.2}, {'price': 2.4081, 'size': 13252.9},...
2025-07-10 01:45:50,850 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:51,021 INFO  📥 CoinAPI 1s message #26: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:50.8440000Z","time_coinapi":"2025-07-09T23:45:51.0198647Z","asks":[{"price":2.4102,"size":5448.9},{"price":2.4103,"size":7904.9},{"price":2.4104,...
2025-07-10 01:45:51,021 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:50.8440000Z', 'time_coinapi': '2025-07-09T23:45:51.0198647Z', 'asks': [{'price': 2.4102, 'size': 5448.9}, {'price': 2.4103, 'size': 7904.9}, {...
2025-07-10 01:45:51,021 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:51,109 INFO  📥 CoinAPI 1s message #27: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:50.9470000Z","time_coinapi":"2025-07-09T23:45:51.1100257Z","asks":[{"price":2.4072,"size":21970.3},{"price":2.4103,"size":66347.8},{"price":2.410...
2025-07-10 01:45:51,110 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:50.9470000Z', 'time_coinapi': '2025-07-09T23:45:51.1100257Z', 'asks': [{'price': 2.4072, 'size': 21970.3}, {'price': 2.4103, 'size': 66347.8},...
2025-07-10 01:45:51,110 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:51,114 INFO  📥 CoinAPI 5m message #7: {"type":"hearbeat"}...
2025-07-10 01:45:51,114 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:51,214 INFO  📥 CoinAPI 1s message #28: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:51.0570000Z","time_coinapi":"2025-07-09T23:45:51.2187325Z","asks":[{"price":2.4082,"size":17328.5}],"bids":[],"symbol_id":"BINANCEFTS_PERP_XRP_US...
2025-07-10 01:45:51,215 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:51.0570000Z', 'time_coinapi': '2025-07-09T23:45:51.2187325Z', 'asks': [{'price': 2.4082, 'size': 17328.5}], 'bids': [], 'symbol_id': 'BINANCEF...
2025-07-10 01:45:51,215 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:51,316 INFO  📥 CoinAPI 1s message #29: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:51.1580000Z","time_coinapi":"2025-07-09T23:45:51.3207031Z","asks":[{"price":2.4063,"size":1515.8},{"price":2.4064,"size":2498.1},{"price":2.4068,...
2025-07-10 01:45:51,316 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:51.1580000Z', 'time_coinapi': '2025-07-09T23:45:51.3207031Z', 'asks': [{'price': 2.4063, 'size': 1515.8}, {'price': 2.4064, 'size': 2498.1}, {...
2025-07-10 01:45:51,316 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:51,340 INFO  📥 CoinAPI 1s message #30: {"time_exchange":"2025-07-09T23:45:51.1800000Z","time_coinapi":"2025-07-09T23:45:51.3429959Z","uuid":"3812bd85-6e69-41d6-a1fa-07862f77c594","price":2.4062,"size":3.8,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:45:51,341 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:45:51.1800000Z', 'time_coinapi': '2025-07-09T23:45:51.3429959Z', 'uuid': '3812bd85-6e69-41d6-a1fa-07862f77c594', 'price': 2.4062, 'size': 3.8, 'taker_side': 'BUY', 'sy...
2025-07-10 01:45:51,341 INFO  📊 CoinAPI trade received for 1s: price=2.4062, size=3.8
2025-07-10 01:45:51,341 INFO  📊 1s OHLCV from trades: $2.405900 vol=157.80
2025-07-10 01:45:51,421 INFO  📥 CoinAPI 1s message #31: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:51.2600000Z","time_coinapi":"2025-07-09T23:45:51.4228254Z","asks":[{"price":2.4062,"size":0},{"price":2.4063,"size":30.2},{"price":2.4065,"size":...
2025-07-10 01:45:51,421 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:51.2600000Z', 'time_coinapi': '2025-07-09T23:45:51.4228254Z', 'asks': [{'price': 2.4062, 'size': 0}, {'price': 2.4063, 'size': 30.2}, {'price'...
2025-07-10 01:45:51,422 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:51,475 INFO  📥 CoinAPI 1s message #32: {"time_exchange":"2025-07-09T23:45:51.3090000Z","time_coinapi":"2025-07-09T23:45:51.4735553Z","uuid":"51fe151e-f043-4bb8-a4cd-5411b9046b80","price":2.4063,"size":23.3,"taker_side":"BUY","symbol_id":"B...
2025-07-10 01:45:51,476 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:45:51.3090000Z', 'time_coinapi': '2025-07-09T23:45:51.4735553Z', 'uuid': '51fe151e-f043-4bb8-a4cd-5411b9046b80', 'price': 2.4063, 'size': 23.3, 'taker_side': 'BUY', 's...
2025-07-10 01:45:51,476 INFO  📊 CoinAPI trade received for 1s: price=2.4063, size=23.3
2025-07-10 01:45:51,532 INFO  📥 CoinAPI 1s message #33: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:51.3740000Z","time_coinapi":"2025-07-09T23:45:51.5368869Z","asks":[{"price":2.4063,"size":0},{"price":2.4064,"size":9425.2},{"price":2.4065,"size...
2025-07-10 01:45:51,533 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:51.3740000Z', 'time_coinapi': '2025-07-09T23:45:51.5368869Z', 'asks': [{'price': 2.4063, 'size': 0}, {'price': 2.4064, 'size': 9425.2}, {'pric...
2025-07-10 01:45:51,533 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:51,647 INFO  📥 CoinAPI 1s message #34: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:51.4890000Z","time_coinapi":"2025-07-09T23:45:51.6526186Z","asks":[{"price":2.4064,"size":9432.1},{"price":2.4072,"size":20430.4},{"price":2.4076...
2025-07-10 01:45:51,648 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:51.4890000Z', 'time_coinapi': '2025-07-09T23:45:51.6526186Z', 'asks': [{'price': 2.4064, 'size': 9432.1}, {'price': 2.4072, 'size': 20430.4}, ...
2025-07-10 01:45:51,648 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:51,768 INFO  📥 CoinAPI 1s message #35: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:51.6080000Z","time_coinapi":"2025-07-09T23:45:51.7709160Z","asks":[{"price":2.4065,"size":509.9},{"price":2.4069,"size":7766.0},{"price":2.4070,"...
2025-07-10 01:45:51,768 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:51.6080000Z', 'time_coinapi': '2025-07-09T23:45:51.7709160Z', 'asks': [{'price': 2.4065, 'size': 509.9}, {'price': 2.4069, 'size': 7766.0}, {'...
2025-07-10 01:45:51,769 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:51,879 INFO  📥 CoinAPI 1s message #36: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:51.7120000Z","time_coinapi":"2025-07-09T23:45:51.8744467Z","asks":[{"price":2.4075,"size":4508.5},{"price":2.4077,"size":17998.2},{"price":2.4082...
2025-07-10 01:45:51,880 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:51.7120000Z', 'time_coinapi': '2025-07-09T23:45:51.8744467Z', 'asks': [{'price': 2.4075, 'size': 4508.5}, {'price': 2.4077, 'size': 17998.2}, ...
2025-07-10 01:45:51,880 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:51,986 INFO  📥 CoinAPI 1s message #37: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:51.8290000Z","time_coinapi":"2025-07-09T23:45:51.9909910Z","asks":[{"price":2.4066,"size":6479.0},{"price":2.4081,"size":16596.2},{"price":2.4083...
2025-07-10 01:45:51,987 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:51.8290000Z', 'time_coinapi': '2025-07-09T23:45:51.9909910Z', 'asks': [{'price': 2.4066, 'size': 6479.0}, {'price': 2.4081, 'size': 16596.2}, ...
2025-07-10 01:45:51,987 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:52,090 INFO  📥 CoinAPI 1s message #38: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:51.9310000Z","time_coinapi":"2025-07-09T23:45:52.0935950Z","asks":[{"price":2.4065,"size":608.6},{"price":2.4069,"size":6922.8},{"price":2.4070,"...
2025-07-10 01:45:52,090 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:51.9310000Z', 'time_coinapi': '2025-07-09T23:45:52.0935950Z', 'asks': [{'price': 2.4065, 'size': 608.6}, {'price': 2.4069, 'size': 6922.8}, {'...
2025-07-10 01:45:52,091 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:52,112 INFO  📥 CoinAPI 5m message #8: {"type":"hearbeat"}...
2025-07-10 01:45:52,112 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:52,212 INFO  📥 CoinAPI 1s message #39: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:52.0430000Z","time_coinapi":"2025-07-09T23:45:52.2059585Z","asks":[{"price":2.4069,"size":7765.9},{"price":2.4070,"size":14863.5}],"bids":[{"pric...
2025-07-10 01:45:52,213 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:52.0430000Z', 'time_coinapi': '2025-07-09T23:45:52.2059585Z', 'asks': [{'price': 2.4069, 'size': 7765.9}, {'price': 2.407, 'size': 14863.5}], ...
2025-07-10 01:45:52,213 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:52,449 INFO  📥 CoinAPI 1s message #40: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:52.2920000Z","time_coinapi":"2025-07-09T23:45:52.4551322Z","asks":[{"price":2.4078,"size":12769.8}],"bids":[{"price":2.4063,"size":6948.0}],"symb...
2025-07-10 01:45:52,449 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:52.2920000Z', 'time_coinapi': '2025-07-09T23:45:52.4551322Z', 'asks': [{'price': 2.4078, 'size': 12769.8}], 'bids': [{'price': 2.4063, 'size':...
2025-07-10 01:45:52,449 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:52,593 INFO  📥 CoinAPI 1s message #41: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:52.4330000Z","time_coinapi":"2025-07-09T23:45:52.5958420Z","asks":[{"price":2.4064,"size":12950.7},{"price":2.4065,"size":3935.5},{"price":2.4066...
2025-07-10 01:45:52,594 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:52.4330000Z', 'time_coinapi': '2025-07-09T23:45:52.5958420Z', 'asks': [{'price': 2.4064, 'size': 12950.7}, {'price': 2.4065, 'size': 3935.5}, ...
2025-07-10 01:45:52,594 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:53,063 INFO  ✅ Proceeding with live trading
2025-07-10 01:45:53,067 INFO  ✅ Initialized data_dict with 1s and 5m timeframes
2025-07-10 01:45:53,068 INFO  🎯 INTELLIGENT EVALUATION SETUP:
2025-07-10 01:45:53,068 INFO     Min evaluation interval: 1.0s
2025-07-10 01:45:53,068 INFO     Max evaluation interval: 10.0s
2025-07-10 01:45:53,068 INFO     Last 5m data timestamp: 2025-07-06 23:59:57+00:00
2025-07-10 01:45:53,068 INFO     Last 1s data timestamp: 2025-07-06 23:59:57+00:00
2025-07-10 01:45:53,068 INFO  ✅ Historical 1s data loaded: 56223 bars from 2025-07-04 00:00:04+00:00 to 2025-07-06 23:59:57+00:00
2025-07-10 01:45:53,068 INFO  ✅ Historical 1s data loaded: 56223 bars from 2025-07-04 00:00:04+00:00 to 2025-07-06 23:59:57+00:00
2025-07-10 01:45:53,072 INFO  📊 NEW 5m DATA: 2025-07-08 22:50:00+00:00, price=2.304700
2025-07-10 01:45:53,072 INFO  📊 5m DataFrame size: 56224 rows
2025-07-10 01:45:53,074 INFO  📊 NEW 5m DATA: 2025-07-08 22:55:00+00:00, price=2.303700
2025-07-10 01:45:53,075 INFO  📊 5m DataFrame size: 56225 rows
2025-07-10 01:45:53,077 INFO  📊 NEW 5m DATA: 2025-07-08 23:00:00+00:00, price=2.305600
2025-07-10 01:45:53,077 INFO  📊 5m DataFrame size: 56226 rows
2025-07-10 01:45:53,080 INFO  📊 NEW 5m DATA: 2025-07-08 23:05:00+00:00, price=2.306400
2025-07-10 01:45:53,080 INFO  📊 5m DataFrame size: 56227 rows
2025-07-10 01:45:53,082 INFO  📊 NEW 5m DATA: 2025-07-08 23:10:00+00:00, price=2.309200
2025-07-10 01:45:53,082 INFO  📊 5m DataFrame size: 56228 rows
2025-07-10 01:45:53,085 INFO  📊 NEW 5m DATA: 2025-07-08 23:15:00+00:00, price=2.309200
2025-07-10 01:45:53,085 INFO  📊 5m DataFrame size: 56229 rows
2025-07-10 01:45:53,087 INFO  📊 NEW 5m DATA: 2025-07-08 23:20:00+00:00, price=2.309800
2025-07-10 01:45:53,087 INFO  📊 5m DataFrame size: 56230 rows
2025-07-10 01:45:53,090 INFO  📊 NEW 5m DATA: 2025-07-08 23:25:00+00:00, price=2.307400
2025-07-10 01:45:53,090 INFO  📊 5m DataFrame size: 56231 rows
2025-07-10 01:45:53,092 INFO  📊 NEW 5m DATA: 2025-07-08 23:30:00+00:00, price=2.304000
2025-07-10 01:45:53,092 INFO  📊 5m DataFrame size: 56232 rows
2025-07-10 01:45:53,095 INFO  📊 NEW 5m DATA: 2025-07-08 23:35:00+00:00, price=2.304800
2025-07-10 01:45:53,095 INFO  📊 5m DataFrame size: 56233 rows
2025-07-10 01:45:53,098 INFO  📊 NEW 5m DATA: 2025-07-08 23:40:00+00:00, price=2.307600
2025-07-10 01:45:53,098 INFO  📊 5m DataFrame size: 56234 rows
2025-07-10 01:45:53,100 INFO  📊 NEW 5m DATA: 2025-07-08 23:45:00+00:00, price=2.308300
2025-07-10 01:45:53,100 INFO  📊 5m DataFrame size: 56235 rows
2025-07-10 01:45:53,102 INFO  📊 NEW 5m DATA: 2025-07-08 23:50:00+00:00, price=2.309000
2025-07-10 01:45:53,102 INFO  📊 5m DataFrame size: 56236 rows
2025-07-10 01:45:53,104 INFO  📊 NEW 5m DATA: 2025-07-08 23:55:00+00:00, price=2.309700
2025-07-10 01:45:53,104 INFO  📊 5m DataFrame size: 56237 rows
2025-07-10 01:45:53,105 INFO  📊 NEW 5m DATA: 2025-07-09 00:00:00+00:00, price=2.310100
2025-07-10 01:45:53,105 INFO  📊 5m DataFrame size: 56238 rows
2025-07-10 01:45:53,108 INFO  📊 NEW 5m DATA: 2025-07-09 00:05:00+00:00, price=2.306400
2025-07-10 01:45:53,108 INFO  📊 5m DataFrame size: 56239 rows
2025-07-10 01:45:53,111 INFO  📊 NEW 5m DATA: 2025-07-09 00:10:00+00:00, price=2.307500
2025-07-10 01:45:53,111 INFO  📊 5m DataFrame size: 56240 rows
2025-07-10 01:45:53,113 INFO  📊 NEW 5m DATA: 2025-07-09 00:15:00+00:00, price=2.306800
2025-07-10 01:45:53,113 INFO  📊 5m DataFrame size: 56241 rows
2025-07-10 01:45:53,116 INFO  📊 NEW 5m DATA: 2025-07-09 00:20:00+00:00, price=2.307300
2025-07-10 01:45:53,116 INFO  📊 5m DataFrame size: 56242 rows
2025-07-10 01:45:53,118 INFO  📊 NEW 5m DATA: 2025-07-09 00:25:00+00:00, price=2.302500
2025-07-10 01:45:53,118 INFO  📊 5m DataFrame size: 56243 rows
2025-07-10 01:45:53,121 INFO  📊 NEW 5m DATA: 2025-07-09 00:30:00+00:00, price=2.300100
2025-07-10 01:45:53,121 INFO  📊 5m DataFrame size: 56244 rows
2025-07-10 01:45:53,122 INFO  📥 CoinAPI 5m message #9: {"type":"hearbeat"}...
2025-07-10 01:45:53,122 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:53,123 INFO  📊 NEW 5m DATA: 2025-07-09 00:35:00+00:00, price=2.298300
2025-07-10 01:45:53,123 INFO  📊 5m DataFrame size: 56245 rows
2025-07-10 01:45:53,125 INFO  📊 NEW 5m DATA: 2025-07-09 00:40:00+00:00, price=2.298200
2025-07-10 01:45:53,125 INFO  📊 5m DataFrame size: 56246 rows
2025-07-10 01:45:53,127 INFO  📊 NEW 5m DATA: 2025-07-09 00:45:00+00:00, price=2.301600
2025-07-10 01:45:53,127 INFO  📊 5m DataFrame size: 56247 rows
2025-07-10 01:45:53,130 INFO  📊 NEW 5m DATA: 2025-07-09 00:50:00+00:00, price=2.302500
2025-07-10 01:45:53,130 INFO  📊 5m DataFrame size: 56248 rows
2025-07-10 01:45:53,133 INFO  📊 NEW 5m DATA: 2025-07-09 00:55:00+00:00, price=2.304500
2025-07-10 01:45:53,133 INFO  📊 5m DataFrame size: 56249 rows
2025-07-10 01:45:53,136 INFO  📊 NEW 5m DATA: 2025-07-09 01:00:00+00:00, price=2.303600
2025-07-10 01:45:53,136 INFO  📊 5m DataFrame size: 56250 rows
2025-07-10 01:45:53,138 INFO  📊 NEW 5m DATA: 2025-07-09 01:05:00+00:00, price=2.308700
2025-07-10 01:45:53,138 INFO  📊 5m DataFrame size: 56251 rows
2025-07-10 01:45:53,140 INFO  📊 NEW 5m DATA: 2025-07-09 01:10:00+00:00, price=2.311100
2025-07-10 01:45:53,140 INFO  📊 5m DataFrame size: 56252 rows
2025-07-10 01:45:53,143 INFO  📊 NEW 5m DATA: 2025-07-09 01:15:00+00:00, price=2.311800
2025-07-10 01:45:53,143 INFO  📊 5m DataFrame size: 56253 rows
2025-07-10 01:45:53,146 INFO  📊 NEW 5m DATA: 2025-07-09 01:20:00+00:00, price=2.309100
2025-07-10 01:45:53,146 INFO  📊 5m DataFrame size: 56254 rows
2025-07-10 01:45:53,148 INFO  📊 NEW 5m DATA: 2025-07-09 01:25:00+00:00, price=2.309800
2025-07-10 01:45:53,148 INFO  📊 5m DataFrame size: 56255 rows
2025-07-10 01:45:53,150 INFO  📊 NEW 5m DATA: 2025-07-09 01:30:00+00:00, price=2.307500
2025-07-10 01:45:53,151 INFO  📊 5m DataFrame size: 56256 rows
2025-07-10 01:45:53,153 INFO  📊 NEW 5m DATA: 2025-07-09 01:35:00+00:00, price=2.305000
2025-07-10 01:45:53,154 INFO  📊 5m DataFrame size: 56257 rows
2025-07-10 01:45:53,156 INFO  📊 NEW 5m DATA: 2025-07-09 01:40:00+00:00, price=2.302800
2025-07-10 01:45:53,156 INFO  📊 5m DataFrame size: 56258 rows
2025-07-10 01:45:53,159 INFO  📊 NEW 5m DATA: 2025-07-09 01:45:00+00:00, price=2.303600
2025-07-10 01:45:53,159 INFO  📊 5m DataFrame size: 56259 rows
2025-07-10 01:45:53,161 INFO  📊 NEW 5m DATA: 2025-07-09 01:50:00+00:00, price=2.305200
2025-07-10 01:45:53,161 INFO  📊 5m DataFrame size: 56260 rows
2025-07-10 01:45:53,163 INFO  📊 NEW 5m DATA: 2025-07-09 01:55:00+00:00, price=2.306900
2025-07-10 01:45:53,163 INFO  📊 5m DataFrame size: 56261 rows
2025-07-10 01:45:53,166 INFO  📊 NEW 5m DATA: 2025-07-09 02:00:00+00:00, price=2.308600
2025-07-10 01:45:53,166 INFO  📊 5m DataFrame size: 56262 rows
2025-07-10 01:45:53,168 INFO  📊 NEW 5m DATA: 2025-07-09 02:05:00+00:00, price=2.310200
2025-07-10 01:45:53,169 INFO  📊 5m DataFrame size: 56263 rows
2025-07-10 01:45:53,171 INFO  📊 NEW 5m DATA: 2025-07-09 02:10:00+00:00, price=2.314900
2025-07-10 01:45:53,171 INFO  📊 5m DataFrame size: 56264 rows
2025-07-10 01:45:53,173 INFO  📊 NEW 5m DATA: 2025-07-09 02:15:00+00:00, price=2.314800
2025-07-10 01:45:53,173 INFO  📊 5m DataFrame size: 56265 rows
2025-07-10 01:45:53,175 INFO  📊 NEW 5m DATA: 2025-07-09 02:20:00+00:00, price=2.310900
2025-07-10 01:45:53,175 INFO  📊 5m DataFrame size: 56266 rows
2025-07-10 01:45:53,178 INFO  📊 NEW 5m DATA: 2025-07-09 02:25:00+00:00, price=2.307400
2025-07-10 01:45:53,178 INFO  📊 5m DataFrame size: 56267 rows
2025-07-10 01:45:53,180 INFO  📊 NEW 5m DATA: 2025-07-09 02:30:00+00:00, price=2.307800
2025-07-10 01:45:53,180 INFO  📊 5m DataFrame size: 56268 rows
2025-07-10 01:45:53,183 INFO  📊 NEW 5m DATA: 2025-07-09 02:35:00+00:00, price=2.306100
2025-07-10 01:45:53,183 INFO  📊 5m DataFrame size: 56269 rows
2025-07-10 01:45:53,187 INFO  📊 NEW 5m DATA: 2025-07-09 02:40:00+00:00, price=2.306900
2025-07-10 01:45:53,187 INFO  📊 5m DataFrame size: 56270 rows
2025-07-10 01:45:53,192 INFO  📊 NEW 5m DATA: 2025-07-09 02:45:00+00:00, price=2.312300
2025-07-10 01:45:53,193 INFO  📊 5m DataFrame size: 56271 rows
2025-07-10 01:45:53,196 INFO  📊 NEW 5m DATA: 2025-07-09 02:50:00+00:00, price=2.311200
2025-07-10 01:45:53,196 INFO  📊 5m DataFrame size: 56272 rows
2025-07-10 01:45:53,198 INFO  📊 NEW 5m DATA: 2025-07-09 02:55:00+00:00, price=2.310600
2025-07-10 01:45:53,198 INFO  📊 5m DataFrame size: 56273 rows
2025-07-10 01:45:53,200 INFO  📊 NEW 5m DATA: 2025-07-09 03:00:00+00:00, price=2.310000
2025-07-10 01:45:53,201 INFO  📊 5m DataFrame size: 56274 rows
2025-07-10 01:45:53,203 INFO  📊 NEW 5m DATA: 2025-07-09 03:05:00+00:00, price=2.310600
2025-07-10 01:45:53,203 INFO  📊 5m DataFrame size: 56275 rows
2025-07-10 01:45:53,205 INFO  📊 NEW 5m DATA: 2025-07-09 03:10:00+00:00, price=2.310900
2025-07-10 01:45:53,205 INFO  📊 5m DataFrame size: 56276 rows
2025-07-10 01:45:53,207 INFO  📊 NEW 5m DATA: 2025-07-09 03:15:00+00:00, price=2.306200
2025-07-10 01:45:53,207 INFO  📊 5m DataFrame size: 56277 rows
2025-07-10 01:45:53,208 INFO  📊 NEW 5m DATA: 2025-07-09 03:20:00+00:00, price=2.304200
2025-07-10 01:45:53,208 INFO  📊 5m DataFrame size: 56278 rows
2025-07-10 01:45:53,211 INFO  📊 NEW 5m DATA: 2025-07-09 03:25:00+00:00, price=2.303000
2025-07-10 01:45:53,211 INFO  📊 5m DataFrame size: 56279 rows
2025-07-10 01:45:53,212 INFO  📊 NEW 5m DATA: 2025-07-09 03:30:00+00:00, price=2.305800
2025-07-10 01:45:53,212 INFO  📊 5m DataFrame size: 56280 rows
2025-07-10 01:45:53,214 INFO  📊 NEW 5m DATA: 2025-07-09 03:35:00+00:00, price=2.302100
2025-07-10 01:45:53,215 INFO  📊 5m DataFrame size: 56281 rows
2025-07-10 01:45:53,217 INFO  📊 NEW 5m DATA: 2025-07-09 03:40:00+00:00, price=2.301300
2025-07-10 01:45:53,217 INFO  📊 5m DataFrame size: 56282 rows
2025-07-10 01:45:53,220 INFO  📊 NEW 5m DATA: 2025-07-09 03:45:00+00:00, price=2.301900
2025-07-10 01:45:53,220 INFO  📊 5m DataFrame size: 56283 rows
2025-07-10 01:45:53,222 INFO  📊 NEW 5m DATA: 2025-07-09 03:50:00+00:00, price=2.303400
2025-07-10 01:45:53,222 INFO  📊 5m DataFrame size: 56284 rows
2025-07-10 01:45:53,224 INFO  📊 NEW 5m DATA: 2025-07-09 03:55:00+00:00, price=2.302000
2025-07-10 01:45:53,224 INFO  📊 5m DataFrame size: 56285 rows
2025-07-10 01:45:53,226 INFO  📊 NEW 5m DATA: 2025-07-09 04:00:00+00:00, price=2.302800
2025-07-10 01:45:53,226 INFO  📊 5m DataFrame size: 56286 rows
2025-07-10 01:45:53,228 INFO  📊 NEW 5m DATA: 2025-07-09 04:05:00+00:00, price=2.303700
2025-07-10 01:45:53,228 INFO  📊 5m DataFrame size: 56287 rows
2025-07-10 01:45:53,230 INFO  📊 NEW 5m DATA: 2025-07-09 04:10:00+00:00, price=2.304600
2025-07-10 01:45:53,230 INFO  📊 5m DataFrame size: 56288 rows
2025-07-10 01:45:53,233 INFO  📊 NEW 5m DATA: 2025-07-09 04:15:00+00:00, price=2.301300
2025-07-10 01:45:53,233 INFO  📊 5m DataFrame size: 56289 rows
2025-07-10 01:45:53,235 INFO  📊 NEW 5m DATA: 2025-07-09 04:20:00+00:00, price=2.303100
2025-07-10 01:45:53,235 INFO  📊 5m DataFrame size: 56290 rows
2025-07-10 01:45:53,237 INFO  📊 NEW 5m DATA: 2025-07-09 04:25:00+00:00, price=2.302600
2025-07-10 01:45:53,237 INFO  📊 5m DataFrame size: 56291 rows
2025-07-10 01:45:53,240 INFO  📊 NEW 5m DATA: 2025-07-09 04:30:00+00:00, price=2.304100
2025-07-10 01:45:53,240 INFO  📊 5m DataFrame size: 56292 rows
2025-07-10 01:45:53,241 INFO  📊 NEW 5m DATA: 2025-07-09 04:35:00+00:00, price=2.305300
2025-07-10 01:45:53,241 INFO  📊 5m DataFrame size: 56293 rows
2025-07-10 01:45:53,243 INFO  📊 NEW 5m DATA: 2025-07-09 04:40:00+00:00, price=2.306100
2025-07-10 01:45:53,243 INFO  📊 5m DataFrame size: 56294 rows
2025-07-10 01:45:53,245 INFO  📊 NEW 5m DATA: 2025-07-09 04:45:00+00:00, price=2.306400
2025-07-10 01:45:53,245 INFO  📊 5m DataFrame size: 56295 rows
2025-07-10 01:45:53,247 INFO  📊 NEW 5m DATA: 2025-07-09 04:50:00+00:00, price=2.309500
2025-07-10 01:45:53,247 INFO  📊 5m DataFrame size: 56296 rows
2025-07-10 01:45:53,248 INFO  📊 NEW 5m DATA: 2025-07-09 04:55:00+00:00, price=2.311500
2025-07-10 01:45:53,248 INFO  📊 5m DataFrame size: 56297 rows
2025-07-10 01:45:53,250 INFO  📊 NEW 5m DATA: 2025-07-09 05:00:00+00:00, price=2.320800
2025-07-10 01:45:53,250 INFO  📊 5m DataFrame size: 56298 rows
2025-07-10 01:45:53,252 INFO  📊 NEW 5m DATA: 2025-07-09 05:05:00+00:00, price=2.321100
2025-07-10 01:45:53,253 INFO  📊 5m DataFrame size: 56299 rows
2025-07-10 01:45:53,254 INFO  📊 NEW 5m DATA: 2025-07-09 05:10:00+00:00, price=2.319900
2025-07-10 01:45:53,255 INFO  📊 5m DataFrame size: 56300 rows
2025-07-10 01:45:53,256 INFO  📊 NEW 5m DATA: 2025-07-09 05:15:00+00:00, price=2.319600
2025-07-10 01:45:53,256 INFO  📊 5m DataFrame size: 56301 rows
2025-07-10 01:45:53,258 INFO  📊 NEW 5m DATA: 2025-07-09 05:20:00+00:00, price=2.318300
2025-07-10 01:45:53,258 INFO  📊 5m DataFrame size: 56302 rows
2025-07-10 01:45:53,261 INFO  📊 NEW 5m DATA: 2025-07-09 05:25:00+00:00, price=2.317100
2025-07-10 01:45:53,261 INFO  📊 5m DataFrame size: 56303 rows
2025-07-10 01:45:53,264 INFO  📊 NEW 5m DATA: 2025-07-09 05:30:00+00:00, price=2.318400
2025-07-10 01:45:53,264 INFO  📊 5m DataFrame size: 56304 rows
2025-07-10 01:45:53,266 INFO  📊 NEW 5m DATA: 2025-07-09 05:35:00+00:00, price=2.320000
2025-07-10 01:45:53,266 INFO  📊 5m DataFrame size: 56305 rows
2025-07-10 01:45:53,268 INFO  📊 NEW 5m DATA: 2025-07-09 05:40:00+00:00, price=2.330500
2025-07-10 01:45:53,268 INFO  📊 5m DataFrame size: 56306 rows
2025-07-10 01:45:53,271 INFO  📊 NEW 5m DATA: 2025-07-09 05:45:00+00:00, price=2.332600
2025-07-10 01:45:53,271 INFO  📊 5m DataFrame size: 56307 rows
2025-07-10 01:45:53,273 INFO  📊 NEW 5m DATA: 2025-07-09 05:50:00+00:00, price=2.328200
2025-07-10 01:45:53,273 INFO  📊 5m DataFrame size: 56308 rows
2025-07-10 01:45:53,275 INFO  📊 NEW 5m DATA: 2025-07-09 05:55:00+00:00, price=2.326000
2025-07-10 01:45:53,275 INFO  📊 5m DataFrame size: 56309 rows
2025-07-10 01:45:53,278 INFO  📊 NEW 5m DATA: 2025-07-09 06:00:00+00:00, price=2.326800
2025-07-10 01:45:53,278 INFO  📊 5m DataFrame size: 56310 rows
2025-07-10 01:45:53,280 INFO  📊 NEW 5m DATA: 2025-07-09 06:05:00+00:00, price=2.324800
2025-07-10 01:45:53,280 INFO  📊 5m DataFrame size: 56311 rows
2025-07-10 01:45:53,283 INFO  📊 NEW 5m DATA: 2025-07-09 06:10:00+00:00, price=2.326500
2025-07-10 01:45:53,283 INFO  📊 5m DataFrame size: 56312 rows
2025-07-10 01:45:53,285 INFO  📊 NEW 5m DATA: 2025-07-09 06:15:00+00:00, price=2.331900
2025-07-10 01:45:53,285 INFO  📊 5m DataFrame size: 56313 rows
2025-07-10 01:45:53,288 INFO  📊 NEW 5m DATA: 2025-07-09 06:20:00+00:00, price=2.336000
2025-07-10 01:45:53,288 INFO  📊 5m DataFrame size: 56314 rows
2025-07-10 01:45:53,290 INFO  📊 NEW 5m DATA: 2025-07-09 06:25:00+00:00, price=2.332100
2025-07-10 01:45:53,290 INFO  📊 5m DataFrame size: 56315 rows
2025-07-10 01:45:53,293 INFO  📊 NEW 5m DATA: 2025-07-09 06:30:00+00:00, price=2.326400
2025-07-10 01:45:53,293 INFO  📊 5m DataFrame size: 56316 rows
2025-07-10 01:45:53,295 INFO  📊 NEW 5m DATA: 2025-07-09 06:35:00+00:00, price=2.324800
2025-07-10 01:45:53,295 INFO  📊 5m DataFrame size: 56317 rows
2025-07-10 01:45:53,298 INFO  📊 NEW 5m DATA: 2025-07-09 06:40:00+00:00, price=2.325800
2025-07-10 01:45:53,298 INFO  📊 5m DataFrame size: 56318 rows
2025-07-10 01:45:53,300 INFO  📊 NEW 5m DATA: 2025-07-09 06:45:00+00:00, price=2.324000
2025-07-10 01:45:53,300 INFO  📊 5m DataFrame size: 56319 rows
2025-07-10 01:45:53,302 INFO  📊 NEW 5m DATA: 2025-07-09 06:50:00+00:00, price=2.323400
2025-07-10 01:45:53,302 INFO  📊 5m DataFrame size: 56320 rows
2025-07-10 01:45:53,304 INFO  📊 NEW 5m DATA: 2025-07-09 06:55:00+00:00, price=2.327300
2025-07-10 01:45:53,304 INFO  📊 5m DataFrame size: 56321 rows
2025-07-10 01:45:53,306 INFO  📊 NEW 5m DATA: 2025-07-09 07:00:00+00:00, price=2.325700
2025-07-10 01:45:53,306 INFO  📊 5m DataFrame size: 56322 rows
2025-07-10 01:45:53,308 INFO  📊 NEW 5m DATA: 2025-07-09 07:05:00+00:00, price=2.326400
2025-07-10 01:45:53,308 INFO  📊 5m DataFrame size: 56323 rows
2025-07-10 01:45:53,310 INFO  📊 NEW 5m DATA: 2025-07-09 07:10:00+00:00, price=2.324800
2025-07-10 01:45:53,310 INFO  📊 5m DataFrame size: 56324 rows
2025-07-10 01:45:53,312 INFO  📊 NEW 5m DATA: 2025-07-09 07:15:00+00:00, price=2.322400
2025-07-10 01:45:53,312 INFO  📊 5m DataFrame size: 56325 rows
2025-07-10 01:45:53,314 INFO  📊 NEW 5m DATA: 2025-07-09 07:20:00+00:00, price=2.325300
2025-07-10 01:45:53,314 INFO  📊 5m DataFrame size: 56326 rows
2025-07-10 01:45:53,316 INFO  📊 NEW 5m DATA: 2025-07-09 07:25:00+00:00, price=2.326900
2025-07-10 01:45:53,316 INFO  📊 5m DataFrame size: 56327 rows
2025-07-10 01:45:53,318 INFO  📊 NEW 5m DATA: 2025-07-09 07:30:00+00:00, price=2.325300
2025-07-10 01:45:53,319 INFO  📊 5m DataFrame size: 56328 rows
2025-07-10 01:45:53,321 INFO  📊 NEW 5m DATA: 2025-07-09 07:35:00+00:00, price=2.325000
2025-07-10 01:45:53,321 INFO  📊 5m DataFrame size: 56329 rows
2025-07-10 01:45:53,323 INFO  📊 NEW 5m DATA: 2025-07-09 07:40:00+00:00, price=2.323900
2025-07-10 01:45:53,323 INFO  📊 5m DataFrame size: 56330 rows
2025-07-10 01:45:53,326 INFO  📊 NEW 5m DATA: 2025-07-09 07:45:00+00:00, price=2.322700
2025-07-10 01:45:53,326 INFO  📊 5m DataFrame size: 56331 rows
2025-07-10 01:45:53,328 INFO  📊 NEW 5m DATA: 2025-07-09 07:50:00+00:00, price=2.331200
2025-07-10 01:45:53,328 INFO  📊 5m DataFrame size: 56332 rows
2025-07-10 01:45:53,330 INFO  📊 NEW 5m DATA: 2025-07-09 07:55:00+00:00, price=2.331900
2025-07-10 01:45:53,330 INFO  📊 5m DataFrame size: 56333 rows
2025-07-10 01:45:53,332 INFO  📊 NEW 5m DATA: 2025-07-09 08:00:00+00:00, price=2.330900
2025-07-10 01:45:53,332 INFO  📊 5m DataFrame size: 56334 rows
2025-07-10 01:45:53,335 INFO  📊 NEW 5m DATA: 2025-07-09 08:05:00+00:00, price=2.331500
2025-07-10 01:45:53,335 INFO  📊 5m DataFrame size: 56335 rows
2025-07-10 01:45:53,337 INFO  📊 NEW 5m DATA: 2025-07-09 08:10:00+00:00, price=2.330700
2025-07-10 01:45:53,337 INFO  📊 5m DataFrame size: 56336 rows
2025-07-10 01:45:53,339 INFO  📊 NEW 5m DATA: 2025-07-09 08:15:00+00:00, price=2.333900
2025-07-10 01:45:53,339 INFO  📊 5m DataFrame size: 56337 rows
2025-07-10 01:45:53,340 INFO  📊 NEW 5m DATA: 2025-07-09 08:20:00+00:00, price=2.336100
2025-07-10 01:45:53,340 INFO  📊 5m DataFrame size: 56338 rows
2025-07-10 01:45:53,342 INFO  📊 NEW 5m DATA: 2025-07-09 08:25:00+00:00, price=2.333700
2025-07-10 01:45:53,342 INFO  📊 5m DataFrame size: 56339 rows
2025-07-10 01:45:53,344 INFO  📊 NEW 5m DATA: 2025-07-09 08:30:00+00:00, price=2.335300
2025-07-10 01:45:53,344 INFO  📊 5m DataFrame size: 56340 rows
2025-07-10 01:45:53,346 INFO  📊 NEW 5m DATA: 2025-07-09 08:35:00+00:00, price=2.338200
2025-07-10 01:45:53,347 INFO  📊 5m DataFrame size: 56341 rows
2025-07-10 01:45:53,348 INFO  📊 NEW 5m DATA: 2025-07-09 08:40:00+00:00, price=2.337600
2025-07-10 01:45:53,348 INFO  📊 5m DataFrame size: 56342 rows
2025-07-10 01:45:53,350 INFO  📊 NEW 5m DATA: 2025-07-09 08:45:00+00:00, price=2.335800
2025-07-10 01:45:53,350 INFO  📊 5m DataFrame size: 56343 rows
2025-07-10 01:45:53,353 INFO  📊 NEW 5m DATA: 2025-07-09 08:50:00+00:00, price=2.334000
2025-07-10 01:45:53,353 INFO  📊 5m DataFrame size: 56344 rows
2025-07-10 01:45:53,355 INFO  📊 NEW 5m DATA: 2025-07-09 08:55:00+00:00, price=2.332800
2025-07-10 01:45:53,356 INFO  📊 5m DataFrame size: 56345 rows
2025-07-10 01:45:53,358 INFO  📊 NEW 5m DATA: 2025-07-09 09:00:00+00:00, price=2.334800
2025-07-10 01:45:53,358 INFO  📊 5m DataFrame size: 56346 rows
2025-07-10 01:45:53,360 INFO  📊 NEW 5m DATA: 2025-07-09 09:05:00+00:00, price=2.332300
2025-07-10 01:45:53,360 INFO  📊 5m DataFrame size: 56347 rows
2025-07-10 01:45:53,362 INFO  📊 NEW 5m DATA: 2025-07-09 09:10:00+00:00, price=2.331100
2025-07-10 01:45:53,362 INFO  📊 5m DataFrame size: 56348 rows
2025-07-10 01:45:53,364 INFO  📊 NEW 5m DATA: 2025-07-09 09:15:00+00:00, price=2.331200
2025-07-10 01:45:53,364 INFO  📊 5m DataFrame size: 56349 rows
2025-07-10 01:45:53,366 INFO  📊 NEW 5m DATA: 2025-07-09 09:20:00+00:00, price=2.333400
2025-07-10 01:45:53,366 INFO  📊 5m DataFrame size: 56350 rows
2025-07-10 01:45:53,368 INFO  📊 NEW 5m DATA: 2025-07-09 09:25:00+00:00, price=2.335100
2025-07-10 01:45:53,368 INFO  📊 5m DataFrame size: 56351 rows
2025-07-10 01:45:53,371 INFO  📊 NEW 5m DATA: 2025-07-09 09:30:00+00:00, price=2.334500
2025-07-10 01:45:53,371 INFO  📊 5m DataFrame size: 56352 rows
2025-07-10 01:45:53,373 INFO  📊 NEW 5m DATA: 2025-07-09 09:35:00+00:00, price=2.334500
2025-07-10 01:45:53,373 INFO  📊 5m DataFrame size: 56353 rows
2025-07-10 01:45:53,375 INFO  📊 NEW 5m DATA: 2025-07-09 09:40:00+00:00, price=2.333600
2025-07-10 01:45:53,376 INFO  📊 5m DataFrame size: 56354 rows
2025-07-10 01:45:53,378 INFO  📊 NEW 5m DATA: 2025-07-09 09:45:00+00:00, price=2.335200
2025-07-10 01:45:53,378 INFO  📊 5m DataFrame size: 56355 rows
2025-07-10 01:45:53,380 INFO  📊 NEW 5m DATA: 2025-07-09 09:50:00+00:00, price=2.336500
2025-07-10 01:45:53,380 INFO  📊 5m DataFrame size: 56356 rows
2025-07-10 01:45:53,382 INFO  📊 NEW 5m DATA: 2025-07-09 09:55:00+00:00, price=2.337600
2025-07-10 01:45:53,382 INFO  📊 5m DataFrame size: 56357 rows
2025-07-10 01:45:53,385 INFO  📊 NEW 5m DATA: 2025-07-09 10:00:00+00:00, price=2.336300
2025-07-10 01:45:53,385 INFO  📊 5m DataFrame size: 56358 rows
2025-07-10 01:45:53,388 INFO  📊 NEW 5m DATA: 2025-07-09 10:05:00+00:00, price=2.338100
2025-07-10 01:45:53,388 INFO  📊 5m DataFrame size: 56359 rows
2025-07-10 01:45:53,390 INFO  📊 NEW 5m DATA: 2025-07-09 10:10:00+00:00, price=2.341400
2025-07-10 01:45:53,390 INFO  📊 5m DataFrame size: 56360 rows
2025-07-10 01:45:53,393 INFO  📊 NEW 5m DATA: 2025-07-09 10:15:00+00:00, price=2.345400
2025-07-10 01:45:53,393 INFO  📊 5m DataFrame size: 56361 rows
2025-07-10 01:45:53,395 INFO  📊 NEW 5m DATA: 2025-07-09 10:20:00+00:00, price=2.343200
2025-07-10 01:45:53,395 INFO  📊 5m DataFrame size: 56362 rows
2025-07-10 01:45:53,397 INFO  📊 NEW 5m DATA: 2025-07-09 10:25:00+00:00, price=2.342400
2025-07-10 01:45:53,397 INFO  📊 5m DataFrame size: 56363 rows
2025-07-10 01:45:53,399 INFO  📊 NEW 5m DATA: 2025-07-09 10:30:00+00:00, price=2.344300
2025-07-10 01:45:53,399 INFO  📊 5m DataFrame size: 56364 rows
2025-07-10 01:45:53,402 INFO  📊 NEW 5m DATA: 2025-07-09 10:35:00+00:00, price=2.344100
2025-07-10 01:45:53,402 INFO  📊 5m DataFrame size: 56365 rows
2025-07-10 01:45:53,404 INFO  📊 NEW 5m DATA: 2025-07-09 10:40:00+00:00, price=2.344000
2025-07-10 01:45:53,404 INFO  📊 5m DataFrame size: 56366 rows
2025-07-10 01:45:53,407 INFO  📊 NEW 5m DATA: 2025-07-09 10:45:00+00:00, price=2.346600
2025-07-10 01:45:53,407 INFO  📊 5m DataFrame size: 56367 rows
2025-07-10 01:45:53,409 INFO  📊 NEW 5m DATA: 2025-07-09 10:50:00+00:00, price=2.355200
2025-07-10 01:45:53,409 INFO  📊 5m DataFrame size: 56368 rows
2025-07-10 01:45:53,411 INFO  📊 NEW 5m DATA: 2025-07-09 10:55:00+00:00, price=2.355300
2025-07-10 01:45:53,411 INFO  📊 5m DataFrame size: 56369 rows
2025-07-10 01:45:53,413 INFO  📊 NEW 5m DATA: 2025-07-09 11:00:00+00:00, price=2.356600
2025-07-10 01:45:53,413 INFO  📊 5m DataFrame size: 56370 rows
2025-07-10 01:45:53,415 INFO  📊 NEW 5m DATA: 2025-07-09 11:05:00+00:00, price=2.354800
2025-07-10 01:45:53,415 INFO  📊 5m DataFrame size: 56371 rows
2025-07-10 01:45:53,417 INFO  📊 NEW 5m DATA: 2025-07-09 11:10:00+00:00, price=2.353500
2025-07-10 01:45:53,417 INFO  📊 5m DataFrame size: 56372 rows
2025-07-10 01:45:53,419 INFO  📊 NEW 5m DATA: 2025-07-09 11:15:00+00:00, price=2.355300
2025-07-10 01:45:53,419 INFO  📊 5m DataFrame size: 56373 rows
2025-07-10 01:45:53,421 INFO  📊 NEW 5m DATA: 2025-07-09 11:20:00+00:00, price=2.377500
2025-07-10 01:45:53,421 INFO  📊 5m DataFrame size: 56374 rows
2025-07-10 01:45:53,423 INFO  📊 NEW 5m DATA: 2025-07-09 11:25:00+00:00, price=2.379300
2025-07-10 01:45:53,423 INFO  📊 5m DataFrame size: 56375 rows
2025-07-10 01:45:53,426 INFO  📊 NEW 5m DATA: 2025-07-09 11:30:00+00:00, price=2.367500
2025-07-10 01:45:53,426 INFO  📊 5m DataFrame size: 56376 rows
2025-07-10 01:45:53,428 INFO  📊 NEW 5m DATA: 2025-07-09 11:35:00+00:00, price=2.377000
2025-07-10 01:45:53,428 INFO  📊 5m DataFrame size: 56377 rows
2025-07-10 01:45:53,430 INFO  📊 NEW 5m DATA: 2025-07-09 11:40:00+00:00, price=2.377700
2025-07-10 01:45:53,430 INFO  📊 5m DataFrame size: 56378 rows
2025-07-10 01:45:53,432 INFO  📊 NEW 5m DATA: 2025-07-09 11:45:00+00:00, price=2.381200
2025-07-10 01:45:53,432 INFO  📊 5m DataFrame size: 56379 rows
2025-07-10 01:45:53,434 INFO  📊 NEW 5m DATA: 2025-07-09 11:50:00+00:00, price=2.384400
2025-07-10 01:45:53,434 INFO  📊 5m DataFrame size: 56380 rows
2025-07-10 01:45:53,437 INFO  📊 NEW 5m DATA: 2025-07-09 11:55:00+00:00, price=2.383900
2025-07-10 01:45:53,437 INFO  📊 5m DataFrame size: 56381 rows
2025-07-10 01:45:53,439 INFO  📊 NEW 5m DATA: 2025-07-09 12:00:00+00:00, price=2.380600
2025-07-10 01:45:53,439 INFO  📊 5m DataFrame size: 56382 rows
2025-07-10 01:45:53,441 INFO  📊 NEW 5m DATA: 2025-07-09 12:05:00+00:00, price=2.389000
2025-07-10 01:45:53,441 INFO  📊 5m DataFrame size: 56383 rows
2025-07-10 01:45:53,442 INFO  📊 NEW 5m DATA: 2025-07-09 12:10:00+00:00, price=2.394800
2025-07-10 01:45:53,442 INFO  📊 5m DataFrame size: 56384 rows
2025-07-10 01:45:53,444 INFO  📊 NEW 5m DATA: 2025-07-09 12:15:00+00:00, price=2.396700
2025-07-10 01:45:53,444 INFO  📊 5m DataFrame size: 56385 rows
2025-07-10 01:45:53,446 INFO  📊 NEW 5m DATA: 2025-07-09 12:20:00+00:00, price=2.389800
2025-07-10 01:45:53,446 INFO  📊 5m DataFrame size: 56386 rows
2025-07-10 01:45:53,448 INFO  📊 NEW 5m DATA: 2025-07-09 12:25:00+00:00, price=2.393200
2025-07-10 01:45:53,448 INFO  📊 5m DataFrame size: 56387 rows
2025-07-10 01:45:53,451 INFO  📊 NEW 5m DATA: 2025-07-09 12:30:00+00:00, price=2.394600
2025-07-10 01:45:53,451 INFO  📊 5m DataFrame size: 56388 rows
2025-07-10 01:45:53,454 INFO  📊 NEW 5m DATA: 2025-07-09 12:35:00+00:00, price=2.394800
2025-07-10 01:45:53,454 INFO  📊 5m DataFrame size: 56389 rows
2025-07-10 01:45:53,456 INFO  📊 NEW 5m DATA: 2025-07-09 12:40:00+00:00, price=2.393700
2025-07-10 01:45:53,456 INFO  📊 5m DataFrame size: 56390 rows
2025-07-10 01:45:53,458 INFO  📊 NEW 5m DATA: 2025-07-09 12:45:00+00:00, price=2.391700
2025-07-10 01:45:53,458 INFO  📊 5m DataFrame size: 56391 rows
2025-07-10 01:45:53,460 INFO  📊 NEW 5m DATA: 2025-07-09 12:50:00+00:00, price=2.387100
2025-07-10 01:45:53,460 INFO  📊 5m DataFrame size: 56392 rows
2025-07-10 01:45:53,462 INFO  📊 NEW 5m DATA: 2025-07-09 12:55:00+00:00, price=2.386300
2025-07-10 01:45:53,462 INFO  📊 5m DataFrame size: 56393 rows
2025-07-10 01:45:53,463 INFO  📊 NEW 5m DATA: 2025-07-09 13:00:00+00:00, price=2.383400
2025-07-10 01:45:53,464 INFO  📊 5m DataFrame size: 56394 rows
2025-07-10 01:45:53,465 INFO  📊 NEW 5m DATA: 2025-07-09 13:05:00+00:00, price=2.397200
2025-07-10 01:45:53,465 INFO  📊 5m DataFrame size: 56395 rows
2025-07-10 01:45:53,467 INFO  📊 NEW 5m DATA: 2025-07-09 13:10:00+00:00, price=2.395500
2025-07-10 01:45:53,467 INFO  📊 5m DataFrame size: 56396 rows
2025-07-10 01:45:53,469 INFO  📊 NEW 5m DATA: 2025-07-09 13:15:00+00:00, price=2.395400
2025-07-10 01:45:53,469 INFO  📊 5m DataFrame size: 56397 rows
2025-07-10 01:45:53,471 INFO  📊 NEW 5m DATA: 2025-07-09 13:20:00+00:00, price=2.390300
2025-07-10 01:45:53,471 INFO  📊 5m DataFrame size: 56398 rows
2025-07-10 01:45:53,473 INFO  📊 NEW 5m DATA: 2025-07-09 13:25:00+00:00, price=2.388500
2025-07-10 01:45:53,473 INFO  📊 5m DataFrame size: 56399 rows
2025-07-10 01:45:53,475 INFO  📊 NEW 5m DATA: 2025-07-09 13:30:00+00:00, price=2.385600
2025-07-10 01:45:53,475 INFO  📊 5m DataFrame size: 56400 rows
2025-07-10 01:45:53,478 INFO  📊 NEW 5m DATA: 2025-07-09 13:35:00+00:00, price=2.384600
2025-07-10 01:45:53,478 INFO  📊 5m DataFrame size: 56401 rows
2025-07-10 01:45:53,480 INFO  📊 NEW 5m DATA: 2025-07-09 13:40:00+00:00, price=2.378700
2025-07-10 01:45:53,480 INFO  📊 5m DataFrame size: 56402 rows
2025-07-10 01:45:53,483 INFO  📊 NEW 5m DATA: 2025-07-09 13:45:00+00:00, price=2.374300
2025-07-10 01:45:53,483 INFO  📊 5m DataFrame size: 56403 rows
2025-07-10 01:45:53,485 INFO  📊 NEW 5m DATA: 2025-07-09 13:50:00+00:00, price=2.379400
2025-07-10 01:45:53,486 INFO  📊 5m DataFrame size: 56404 rows
2025-07-10 01:45:53,488 INFO  📊 NEW 5m DATA: 2025-07-09 13:55:00+00:00, price=2.376900
2025-07-10 01:45:53,488 INFO  📊 5m DataFrame size: 56405 rows
2025-07-10 01:45:53,490 INFO  📊 NEW 5m DATA: 2025-07-09 14:00:00+00:00, price=2.367400
2025-07-10 01:45:53,490 INFO  📊 5m DataFrame size: 56406 rows
2025-07-10 01:45:53,492 INFO  📊 NEW 5m DATA: 2025-07-09 14:05:00+00:00, price=2.359000
2025-07-10 01:45:53,492 INFO  📊 5m DataFrame size: 56407 rows
2025-07-10 01:45:53,494 INFO  📊 NEW 5m DATA: 2025-07-09 14:10:00+00:00, price=2.363800
2025-07-10 01:45:53,494 INFO  📊 5m DataFrame size: 56408 rows
2025-07-10 01:45:53,495 INFO  📊 NEW 5m DATA: 2025-07-09 14:15:00+00:00, price=2.364800
2025-07-10 01:45:53,496 INFO  📊 5m DataFrame size: 56409 rows
2025-07-10 01:45:53,498 INFO  📊 NEW 5m DATA: 2025-07-09 14:20:00+00:00, price=2.371300
2025-07-10 01:45:53,498 INFO  📊 5m DataFrame size: 56410 rows
2025-07-10 01:45:53,500 INFO  📊 NEW 5m DATA: 2025-07-09 14:25:00+00:00, price=2.374300
2025-07-10 01:45:53,500 INFO  📊 5m DataFrame size: 56411 rows
2025-07-10 01:45:53,502 INFO  📊 NEW 5m DATA: 2025-07-09 14:30:00+00:00, price=2.361200
2025-07-10 01:45:53,502 INFO  📊 5m DataFrame size: 56412 rows
2025-07-10 01:45:53,504 INFO  📊 NEW 5m DATA: 2025-07-09 14:35:00+00:00, price=2.361200
2025-07-10 01:45:53,504 INFO  📊 5m DataFrame size: 56413 rows
2025-07-10 01:45:53,506 INFO  📊 NEW 5m DATA: 2025-07-09 14:40:00+00:00, price=2.360400
2025-07-10 01:45:53,506 INFO  📊 5m DataFrame size: 56414 rows
2025-07-10 01:45:53,509 INFO  📊 NEW 5m DATA: 2025-07-09 14:45:00+00:00, price=2.358500
2025-07-10 01:45:53,509 INFO  📊 5m DataFrame size: 56415 rows
2025-07-10 01:45:53,512 INFO  📊 NEW 5m DATA: 2025-07-09 14:50:00+00:00, price=2.355400
2025-07-10 01:45:53,512 INFO  📊 5m DataFrame size: 56416 rows
2025-07-10 01:45:53,514 INFO  📊 NEW 5m DATA: 2025-07-09 14:55:00+00:00, price=2.363200
2025-07-10 01:45:53,514 INFO  📊 5m DataFrame size: 56417 rows
2025-07-10 01:45:53,515 INFO  📊 NEW 5m DATA: 2025-07-09 15:00:00+00:00, price=2.371600
2025-07-10 01:45:53,515 INFO  📊 5m DataFrame size: 56418 rows
2025-07-10 01:45:53,517 INFO  📊 NEW 5m DATA: 2025-07-09 15:05:00+00:00, price=2.376700
2025-07-10 01:45:53,517 INFO  📊 5m DataFrame size: 56419 rows
2025-07-10 01:45:53,520 INFO  📊 NEW 5m DATA: 2025-07-09 15:10:00+00:00, price=2.371700
2025-07-10 01:45:53,520 INFO  📊 5m DataFrame size: 56420 rows
2025-07-10 01:45:53,522 INFO  📊 NEW 5m DATA: 2025-07-09 15:15:00+00:00, price=2.368100
2025-07-10 01:45:53,522 INFO  📊 5m DataFrame size: 56421 rows
2025-07-10 01:45:53,524 INFO  📊 NEW 5m DATA: 2025-07-09 15:20:00+00:00, price=2.376600
2025-07-10 01:45:53,524 INFO  📊 5m DataFrame size: 56422 rows
2025-07-10 01:45:53,526 INFO  📊 NEW 5m DATA: 2025-07-09 15:25:00+00:00, price=2.377700
2025-07-10 01:45:53,526 INFO  📊 5m DataFrame size: 56423 rows
2025-07-10 01:45:53,528 INFO  📊 NEW 5m DATA: 2025-07-09 15:30:00+00:00, price=2.379700
2025-07-10 01:45:53,528 INFO  📊 5m DataFrame size: 56424 rows
2025-07-10 01:45:53,529 INFO  📊 NEW 5m DATA: 2025-07-09 15:35:00+00:00, price=2.378600
2025-07-10 01:45:53,529 INFO  📊 5m DataFrame size: 56425 rows
2025-07-10 01:45:53,532 INFO  📊 NEW 5m DATA: 2025-07-09 15:40:00+00:00, price=2.376500
2025-07-10 01:45:53,532 INFO  📊 5m DataFrame size: 56426 rows
2025-07-10 01:45:53,535 INFO  📊 NEW 5m DATA: 2025-07-09 15:45:00+00:00, price=2.376800
2025-07-10 01:45:53,535 INFO  📊 5m DataFrame size: 56427 rows
2025-07-10 01:45:53,537 INFO  📊 NEW 5m DATA: 2025-07-09 15:50:00+00:00, price=2.376900
2025-07-10 01:45:53,537 INFO  📊 5m DataFrame size: 56428 rows
2025-07-10 01:45:53,539 INFO  📊 NEW 5m DATA: 2025-07-09 15:55:00+00:00, price=2.378800
2025-07-10 01:45:53,539 INFO  📊 5m DataFrame size: 56429 rows
2025-07-10 01:45:53,541 INFO  📊 NEW 5m DATA: 2025-07-09 16:00:00+00:00, price=2.381000
2025-07-10 01:45:53,541 INFO  📊 5m DataFrame size: 56430 rows
2025-07-10 01:45:53,543 INFO  📊 NEW 5m DATA: 2025-07-09 16:05:00+00:00, price=2.377500
2025-07-10 01:45:53,543 INFO  📊 5m DataFrame size: 56431 rows
2025-07-10 01:45:53,545 INFO  📊 NEW 5m DATA: 2025-07-09 16:10:00+00:00, price=2.381700
2025-07-10 01:45:53,546 INFO  📊 5m DataFrame size: 56432 rows
2025-07-10 01:45:53,548 INFO  📊 NEW 5m DATA: 2025-07-09 16:15:00+00:00, price=2.382000
2025-07-10 01:45:53,548 INFO  📊 5m DataFrame size: 56433 rows
2025-07-10 01:45:53,550 INFO  📊 NEW 5m DATA: 2025-07-09 16:20:00+00:00, price=2.386000
2025-07-10 01:45:53,550 INFO  📊 5m DataFrame size: 56434 rows
2025-07-10 01:45:53,553 INFO  📊 NEW 5m DATA: 2025-07-09 16:25:00+00:00, price=2.377000
2025-07-10 01:45:53,553 INFO  📊 5m DataFrame size: 56435 rows
2025-07-10 01:45:53,555 INFO  📊 NEW 5m DATA: 2025-07-09 16:30:00+00:00, price=2.375800
2025-07-10 01:45:53,555 INFO  📊 5m DataFrame size: 56436 rows
2025-07-10 01:45:53,557 INFO  📊 NEW 5m DATA: 2025-07-09 16:35:00+00:00, price=2.375500
2025-07-10 01:45:53,558 INFO  📊 5m DataFrame size: 56437 rows
2025-07-10 01:45:53,560 INFO  📊 NEW 5m DATA: 2025-07-09 16:40:00+00:00, price=2.379500
2025-07-10 01:45:53,560 INFO  📊 5m DataFrame size: 56438 rows
2025-07-10 01:45:53,562 INFO  📊 NEW 5m DATA: 2025-07-09 16:45:00+00:00, price=2.379100
2025-07-10 01:45:53,562 INFO  📊 5m DataFrame size: 56439 rows
2025-07-10 01:45:53,564 INFO  📊 NEW 5m DATA: 2025-07-09 16:50:00+00:00, price=2.385900
2025-07-10 01:45:53,564 INFO  📊 5m DataFrame size: 56440 rows
2025-07-10 01:45:53,566 INFO  📊 NEW 5m DATA: 2025-07-09 16:55:00+00:00, price=2.382100
2025-07-10 01:45:53,566 INFO  📊 5m DataFrame size: 56441 rows
2025-07-10 01:45:53,567 INFO  📊 NEW 5m DATA: 2025-07-09 17:00:00+00:00, price=2.384400
2025-07-10 01:45:53,567 INFO  📊 5m DataFrame size: 56442 rows
2025-07-10 01:45:53,569 INFO  📊 NEW 5m DATA: 2025-07-09 17:05:00+00:00, price=2.376100
2025-07-10 01:45:53,569 INFO  📊 5m DataFrame size: 56443 rows
2025-07-10 01:45:53,571 INFO  📊 NEW 5m DATA: 2025-07-09 17:10:00+00:00, price=2.380200
2025-07-10 01:45:53,571 INFO  📊 5m DataFrame size: 56444 rows
2025-07-10 01:45:53,573 INFO  📊 NEW 5m DATA: 2025-07-09 17:15:00+00:00, price=2.378100
2025-07-10 01:45:53,573 INFO  📊 5m DataFrame size: 56445 rows
2025-07-10 01:45:53,574 INFO  📊 NEW 5m DATA: 2025-07-09 17:20:00+00:00, price=2.378700
2025-07-10 01:45:53,574 INFO  📊 5m DataFrame size: 56446 rows
2025-07-10 01:45:53,575 INFO  📊 NEW 5m DATA: 2025-07-09 17:25:00+00:00, price=2.378500
2025-07-10 01:45:53,575 INFO  📊 5m DataFrame size: 56447 rows
2025-07-10 01:45:53,577 INFO  📊 NEW 5m DATA: 2025-07-09 17:30:00+00:00, price=2.378400
2025-07-10 01:45:53,577 INFO  📊 5m DataFrame size: 56448 rows
2025-07-10 01:45:53,579 INFO  📊 NEW 5m DATA: 2025-07-09 17:35:00+00:00, price=2.380200
2025-07-10 01:45:53,579 INFO  📊 5m DataFrame size: 56449 rows
2025-07-10 01:45:53,582 INFO  📊 NEW 5m DATA: 2025-07-09 17:40:00+00:00, price=2.378100
2025-07-10 01:45:53,582 INFO  📊 5m DataFrame size: 56450 rows
2025-07-10 01:45:53,583 INFO  📊 NEW 5m DATA: 2025-07-09 17:45:00+00:00, price=2.378300
2025-07-10 01:45:53,583 INFO  📊 5m DataFrame size: 56451 rows
2025-07-10 01:45:53,586 INFO  📊 NEW 5m DATA: 2025-07-09 17:50:00+00:00, price=2.380000
2025-07-10 01:45:53,586 INFO  📊 5m DataFrame size: 56452 rows
2025-07-10 01:45:53,587 INFO  📊 NEW 5m DATA: 2025-07-09 17:55:00+00:00, price=2.379400
2025-07-10 01:45:53,587 INFO  📊 5m DataFrame size: 56453 rows
2025-07-10 01:45:53,590 INFO  📊 NEW 5m DATA: 2025-07-09 18:00:00+00:00, price=2.379800
2025-07-10 01:45:53,590 INFO  📊 5m DataFrame size: 56454 rows
2025-07-10 01:45:53,591 INFO  📥 CoinAPI 1s message #42: {"type":"hearbeat"}...
2025-07-10 01:45:53,591 INFO  📊 CoinAPI 1s parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:53,593 INFO  📊 NEW 5m DATA: 2025-07-09 18:05:00+00:00, price=2.382500
2025-07-10 01:45:53,593 INFO  📊 5m DataFrame size: 56455 rows
2025-07-10 01:45:53,596 INFO  📊 NEW 5m DATA: 2025-07-09 18:10:00+00:00, price=2.385900
2025-07-10 01:45:53,596 INFO  📊 5m DataFrame size: 56456 rows
2025-07-10 01:45:53,597 INFO  📊 NEW 5m DATA: 2025-07-09 18:15:00+00:00, price=2.390900
2025-07-10 01:45:53,598 INFO  📊 5m DataFrame size: 56457 rows
2025-07-10 01:45:53,599 INFO  📊 NEW 5m DATA: 2025-07-09 18:20:00+00:00, price=2.391200
2025-07-10 01:45:53,599 INFO  📊 5m DataFrame size: 56458 rows
2025-07-10 01:45:53,602 INFO  📊 NEW 5m DATA: 2025-07-09 18:25:00+00:00, price=2.394300
2025-07-10 01:45:53,602 INFO  📊 5m DataFrame size: 56459 rows
2025-07-10 01:45:53,604 INFO  📊 NEW 5m DATA: 2025-07-09 18:30:00+00:00, price=2.396600
2025-07-10 01:45:53,604 INFO  📊 5m DataFrame size: 56460 rows
2025-07-10 01:45:53,606 INFO  📊 NEW 5m DATA: 2025-07-09 18:35:00+00:00, price=2.387000
2025-07-10 01:45:53,606 INFO  📊 5m DataFrame size: 56461 rows
2025-07-10 01:45:53,608 INFO  📊 NEW 5m DATA: 2025-07-09 18:40:00+00:00, price=2.384900
2025-07-10 01:45:53,608 INFO  📊 5m DataFrame size: 56462 rows
2025-07-10 01:45:53,609 INFO  📥 CoinAPI 1s message #43: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:53.4350000Z","time_coinapi":"2025-07-09T23:45:53.5983381Z","asks":[{"price":2.4064,"size":14443.2},{"price":2.4065,"size":2443.0},{"price":2.4067...
2025-07-10 01:45:53,609 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:53.4350000Z', 'time_coinapi': '2025-07-09T23:45:53.5983381Z', 'asks': [{'price': 2.4064, 'size': 14443.2}, {'price': 2.4065, 'size': 2443.0}, ...
2025-07-10 01:45:53,609 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:53,610 INFO  📊 NEW 5m DATA: 2025-07-09 18:45:00+00:00, price=2.384900
2025-07-10 01:45:53,610 INFO  📊 5m DataFrame size: 56463 rows
2025-07-10 01:45:53,613 INFO  📊 NEW 5m DATA: 2025-07-09 18:50:00+00:00, price=2.385800
2025-07-10 01:45:53,613 INFO  📊 5m DataFrame size: 56464 rows
2025-07-10 01:45:53,614 INFO  📊 NEW 5m DATA: 2025-07-09 18:55:00+00:00, price=2.387700
2025-07-10 01:45:53,614 INFO  📊 5m DataFrame size: 56465 rows
2025-07-10 01:45:53,616 INFO  📊 NEW 5m DATA: 2025-07-09 19:00:00+00:00, price=2.389400
2025-07-10 01:45:53,616 INFO  📊 5m DataFrame size: 56466 rows
2025-07-10 01:45:53,619 INFO  📊 NEW 5m DATA: 2025-07-09 19:05:00+00:00, price=2.394500
2025-07-10 01:45:53,620 INFO  📊 5m DataFrame size: 56467 rows
2025-07-10 01:45:53,622 INFO  📊 NEW 5m DATA: 2025-07-09 19:10:00+00:00, price=2.393500
2025-07-10 01:45:53,622 INFO  📊 5m DataFrame size: 56468 rows
2025-07-10 01:45:53,623 INFO  📊 NEW 5m DATA: 2025-07-09 19:15:00+00:00, price=2.395500
2025-07-10 01:45:53,623 INFO  📊 5m DataFrame size: 56469 rows
2025-07-10 01:45:53,625 INFO  📊 NEW 5m DATA: 2025-07-09 19:20:00+00:00, price=2.398000
2025-07-10 01:45:53,625 INFO  📊 5m DataFrame size: 56470 rows
2025-07-10 01:45:53,627 INFO  📊 NEW 5m DATA: 2025-07-09 19:25:00+00:00, price=2.407000
2025-07-10 01:45:53,627 INFO  📊 5m DataFrame size: 56471 rows
2025-07-10 01:45:53,629 INFO  📊 NEW 5m DATA: 2025-07-09 19:30:00+00:00, price=2.407300
2025-07-10 01:45:53,629 INFO  📊 5m DataFrame size: 56472 rows
2025-07-10 01:45:53,631 INFO  📊 NEW 5m DATA: 2025-07-09 19:35:00+00:00, price=2.405400
2025-07-10 01:45:53,631 INFO  📊 5m DataFrame size: 56473 rows
2025-07-10 01:45:53,634 INFO  📊 NEW 5m DATA: 2025-07-09 19:40:00+00:00, price=2.409800
2025-07-10 01:45:53,634 INFO  📊 5m DataFrame size: 56474 rows
2025-07-10 01:45:53,636 INFO  📊 NEW 5m DATA: 2025-07-09 19:45:00+00:00, price=2.413100
2025-07-10 01:45:53,636 INFO  📊 5m DataFrame size: 56475 rows
2025-07-10 01:45:53,639 INFO  📊 NEW 5m DATA: 2025-07-09 19:50:00+00:00, price=2.417500
2025-07-10 01:45:53,639 INFO  📊 5m DataFrame size: 56476 rows
2025-07-10 01:45:53,641 INFO  📊 NEW 5m DATA: 2025-07-09 19:55:00+00:00, price=2.424600
2025-07-10 01:45:53,641 INFO  📊 5m DataFrame size: 56477 rows
2025-07-10 01:45:53,644 INFO  📊 NEW 5m DATA: 2025-07-09 20:00:00+00:00, price=2.415100
2025-07-10 01:45:53,644 INFO  📊 5m DataFrame size: 56478 rows
2025-07-10 01:45:53,647 INFO  📊 NEW 5m DATA: 2025-07-09 20:05:00+00:00, price=2.405300
2025-07-10 01:45:53,647 INFO  📊 5m DataFrame size: 56479 rows
2025-07-10 01:45:53,649 INFO  📊 NEW 5m DATA: 2025-07-09 20:10:00+00:00, price=2.398500
2025-07-10 01:45:53,649 INFO  📊 5m DataFrame size: 56480 rows
2025-07-10 01:45:53,651 INFO  📊 NEW 5m DATA: 2025-07-09 20:15:00+00:00, price=2.400400
2025-07-10 01:45:53,651 INFO  📊 5m DataFrame size: 56481 rows
2025-07-10 01:45:53,653 INFO  📊 NEW 5m DATA: 2025-07-09 20:20:00+00:00, price=2.396600
2025-07-10 01:45:53,653 INFO  📊 5m DataFrame size: 56482 rows
2025-07-10 01:45:53,655 INFO  📊 NEW 5m DATA: 2025-07-09 20:25:00+00:00, price=2.390200
2025-07-10 01:45:53,655 INFO  📊 5m DataFrame size: 56483 rows
2025-07-10 01:45:53,657 INFO  📊 NEW 5m DATA: 2025-07-09 20:30:00+00:00, price=2.390400
2025-07-10 01:45:53,657 INFO  📊 5m DataFrame size: 56484 rows
2025-07-10 01:45:53,659 INFO  📊 NEW 5m DATA: 2025-07-09 20:35:00+00:00, price=2.391200
2025-07-10 01:45:53,659 INFO  📊 5m DataFrame size: 56485 rows
2025-07-10 01:45:53,661 INFO  📊 NEW 5m DATA: 2025-07-09 20:40:00+00:00, price=2.391300
2025-07-10 01:45:53,662 INFO  📊 5m DataFrame size: 56486 rows
2025-07-10 01:45:53,664 INFO  📊 NEW 5m DATA: 2025-07-09 20:45:00+00:00, price=2.390400
2025-07-10 01:45:53,664 INFO  📊 5m DataFrame size: 56487 rows
2025-07-10 01:45:53,666 INFO  📊 NEW 5m DATA: 2025-07-09 20:50:00+00:00, price=2.391000
2025-07-10 01:45:53,666 INFO  📊 5m DataFrame size: 56488 rows
2025-07-10 01:45:53,669 INFO  📊 NEW 5m DATA: 2025-07-09 20:55:00+00:00, price=2.389300
2025-07-10 01:45:53,669 INFO  📊 5m DataFrame size: 56489 rows
2025-07-10 01:45:53,671 INFO  📊 NEW 5m DATA: 2025-07-09 21:00:00+00:00, price=2.393000
2025-07-10 01:45:53,671 INFO  📊 5m DataFrame size: 56490 rows
2025-07-10 01:45:53,673 INFO  📊 NEW 5m DATA: 2025-07-09 21:05:00+00:00, price=2.399600
2025-07-10 01:45:53,673 INFO  📊 5m DataFrame size: 56491 rows
2025-07-10 01:45:53,674 INFO  📊 NEW 5m DATA: 2025-07-09 21:10:00+00:00, price=2.403000
2025-07-10 01:45:53,674 INFO  📊 5m DataFrame size: 56492 rows
2025-07-10 01:45:53,676 INFO  📊 NEW 5m DATA: 2025-07-09 21:15:00+00:00, price=2.396500
2025-07-10 01:45:53,677 INFO  📊 5m DataFrame size: 56493 rows
2025-07-10 01:45:53,678 INFO  📊 NEW 5m DATA: 2025-07-09 21:20:00+00:00, price=2.402000
2025-07-10 01:45:53,678 INFO  📊 5m DataFrame size: 56494 rows
2025-07-10 01:45:53,680 INFO  📊 NEW 5m DATA: 2025-07-09 21:25:00+00:00, price=2.403300
2025-07-10 01:45:53,680 INFO  📊 5m DataFrame size: 56495 rows
2025-07-10 01:45:53,682 INFO  📊 NEW 5m DATA: 2025-07-09 21:30:00+00:00, price=2.407900
2025-07-10 01:45:53,682 INFO  📊 5m DataFrame size: 56496 rows
2025-07-10 01:45:53,684 INFO  📊 NEW 5m DATA: 2025-07-09 21:35:00+00:00, price=2.409500
2025-07-10 01:45:53,684 INFO  📊 5m DataFrame size: 56497 rows
2025-07-10 01:45:53,686 INFO  📊 NEW 5m DATA: 2025-07-09 21:40:00+00:00, price=2.404500
2025-07-10 01:45:53,686 INFO  📊 5m DataFrame size: 56498 rows
2025-07-10 01:45:53,688 INFO  📊 NEW 5m DATA: 2025-07-09 21:45:00+00:00, price=2.408000
2025-07-10 01:45:53,688 INFO  📊 5m DataFrame size: 56499 rows
2025-07-10 01:45:53,690 INFO  📊 NEW 5m DATA: 2025-07-09 21:50:00+00:00, price=2.411200
2025-07-10 01:45:53,690 INFO  📊 5m DataFrame size: 56500 rows
2025-07-10 01:45:53,692 INFO  📊 NEW 5m DATA: 2025-07-09 21:55:00+00:00, price=2.412500
2025-07-10 01:45:53,692 INFO  📊 5m DataFrame size: 56501 rows
2025-07-10 01:45:53,694 INFO  📊 NEW 5m DATA: 2025-07-09 22:00:00+00:00, price=2.410400
2025-07-10 01:45:53,694 INFO  📊 5m DataFrame size: 56502 rows
2025-07-10 01:45:53,696 INFO  📊 NEW 5m DATA: 2025-07-09 22:05:00+00:00, price=2.410400
2025-07-10 01:45:53,696 INFO  📊 5m DataFrame size: 56503 rows
2025-07-10 01:45:53,698 INFO  📊 NEW 5m DATA: 2025-07-09 22:10:00+00:00, price=2.413600
2025-07-10 01:45:53,698 INFO  📊 5m DataFrame size: 56504 rows
2025-07-10 01:45:53,700 INFO  📊 NEW 5m DATA: 2025-07-09 22:15:00+00:00, price=2.415600
2025-07-10 01:45:53,700 INFO  📊 5m DataFrame size: 56505 rows
2025-07-10 01:45:53,703 INFO  📊 NEW 5m DATA: 2025-07-09 22:20:00+00:00, price=2.414800
2025-07-10 01:45:53,703 INFO  📊 5m DataFrame size: 56506 rows
2025-07-10 01:45:53,705 INFO  📊 NEW 5m DATA: 2025-07-09 22:25:00+00:00, price=2.413300
2025-07-10 01:45:53,706 INFO  📊 5m DataFrame size: 56507 rows
2025-07-10 01:45:53,707 INFO  📥 CoinAPI 1s message #44: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:53.5470000Z","time_coinapi":"2025-07-09T23:45:53.7093065Z","asks":[],"bids":[{"price":2.4063,"size":6148.8},{"price":2.4059,"size":10330.3}],"sym...
2025-07-10 01:45:53,707 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:53.5470000Z', 'time_coinapi': '2025-07-09T23:45:53.7093065Z', 'asks': [], 'bids': [{'price': 2.4063, 'size': 6148.8}, {'price': 2.4059, 'size'...
2025-07-10 01:45:53,707 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:53,709 INFO  📊 NEW 5m DATA: 2025-07-09 22:30:00+00:00, price=2.410600
2025-07-10 01:45:53,709 INFO  📊 5m DataFrame size: 56508 rows
2025-07-10 01:45:53,711 INFO  📊 NEW 5m DATA: 2025-07-09 22:35:00+00:00, price=2.412600
2025-07-10 01:45:53,711 INFO  📊 5m DataFrame size: 56509 rows
2025-07-10 01:45:53,713 INFO  📊 NEW 5m DATA: 2025-07-09 22:40:00+00:00, price=2.414800
2025-07-10 01:45:53,713 INFO  📊 5m DataFrame size: 56510 rows
2025-07-10 01:45:53,715 INFO  📊 NEW 5m DATA: 2025-07-09 22:45:00+00:00, price=2.413900
2025-07-10 01:45:53,715 INFO  📊 5m DataFrame size: 56511 rows
2025-07-10 01:45:53,717 INFO  📊 NEW 5m DATA: 2025-07-09 22:50:00+00:00, price=2.416400
2025-07-10 01:45:53,717 INFO  📊 5m DataFrame size: 56512 rows
2025-07-10 01:45:53,720 INFO  📊 NEW 5m DATA: 2025-07-09 22:55:00+00:00, price=2.414400
2025-07-10 01:45:53,720 INFO  📊 5m DataFrame size: 56513 rows
2025-07-10 01:45:53,723 INFO  📊 NEW 5m DATA: 2025-07-09 23:00:00+00:00, price=2.417000
2025-07-10 01:45:53,723 INFO  📊 5m DataFrame size: 56514 rows
2025-07-10 01:45:53,725 INFO  📊 NEW 5m DATA: 2025-07-09 23:05:00+00:00, price=2.412700
2025-07-10 01:45:53,726 INFO  📊 5m DataFrame size: 56515 rows
2025-07-10 01:45:53,728 INFO  📊 NEW 5m DATA: 2025-07-09 23:10:00+00:00, price=2.413600
2025-07-10 01:45:53,728 INFO  📊 5m DataFrame size: 56516 rows
2025-07-10 01:45:53,730 INFO  📊 NEW 5m DATA: 2025-07-09 23:15:00+00:00, price=2.411400
2025-07-10 01:45:53,730 INFO  📊 5m DataFrame size: 56517 rows
2025-07-10 01:45:53,732 INFO  📊 NEW 5m DATA: 2025-07-09 23:20:00+00:00, price=2.414000
2025-07-10 01:45:53,732 INFO  📊 5m DataFrame size: 56518 rows
2025-07-10 01:45:53,734 INFO  📊 NEW 5m DATA: 2025-07-09 23:25:00+00:00, price=2.413300
2025-07-10 01:45:53,734 INFO  📊 5m DataFrame size: 56519 rows
2025-07-10 01:45:53,736 INFO  📊 NEW 5m DATA: 2025-07-09 23:30:00+00:00, price=2.412300
2025-07-10 01:45:53,736 INFO  📊 5m DataFrame size: 56520 rows
2025-07-10 01:45:53,738 INFO  📊 NEW 5m DATA: 2025-07-09 23:35:00+00:00, price=2.408900
2025-07-10 01:45:53,738 INFO  📊 5m DataFrame size: 56521 rows
2025-07-10 01:45:53,740 INFO  📊 NEW 5m DATA: 2025-07-09 23:40:00+00:00, price=2.408700
2025-07-10 01:45:53,740 INFO  📊 5m DataFrame size: 56522 rows
2025-07-10 01:45:53,741 INFO  📊 NEW 5m DATA: 2025-07-09 23:45:00+00:00, price=2.405400
2025-07-10 01:45:53,742 INFO  📊 5m DataFrame size: 56523 rows
2025-07-10 01:45:53,866 INFO  📥 CoinAPI 1s message #45: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:53.6860000Z","time_coinapi":"2025-07-09T23:45:53.8506457Z","asks":[],"bids":[{"price":2.4059,"size":10747.8}],"symbol_id":"BINANCEFTS_PERP_XRP_US...
2025-07-10 01:45:53,867 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:53.6860000Z', 'time_coinapi': '2025-07-09T23:45:53.8506457Z', 'asks': [], 'bids': [{'price': 2.4059, 'size': 10747.8}], 'symbol_id': 'BINANCEF...
2025-07-10 01:45:53,867 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:54,042 INFO  📥 CoinAPI 1s message #46: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:53.8660000Z","time_coinapi":"2025-07-09T23:45:54.0290761Z","asks":[{"price":2.4077,"size":16151.2},{"price":2.4079,"size":8134.1},{"price":2.4102...
2025-07-10 01:45:54,043 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:53.8660000Z', 'time_coinapi': '2025-07-09T23:45:54.0290761Z', 'asks': [{'price': 2.4077, 'size': 16151.2}, {'price': 2.4079, 'size': 8134.1}, ...
2025-07-10 01:45:54,044 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:54,111 INFO  📥 CoinAPI 5m message #10: {"type":"hearbeat"}...
2025-07-10 01:45:54,111 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:54,271 INFO  📥 CoinAPI 1s message #47: {"time_exchange":"2025-07-09T23:45:53.9790000Z","time_coinapi":"2025-07-09T23:45:54.1436932Z","uuid":"eb5c632e-db51-45dc-a329-1e8fa9aa525f","price":2.4063,"size":442.4,"taker_side":"SELL","symbol_id":...
2025-07-10 01:45:54,272 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:45:53.9790000Z', 'time_coinapi': '2025-07-09T23:45:54.1436932Z', 'uuid': 'eb5c632e-db51-45dc-a329-1e8fa9aa525f', 'price': 2.4063, 'size': 442.4, 'taker_side': 'SELL', ...
2025-07-10 01:45:54,272 INFO  📊 CoinAPI trade received for 1s: price=2.4063, size=442.4
2025-07-10 01:45:54,272 INFO  📊 1s OHLCV from trades: $2.406300 vol=27.10
2025-07-10 01:45:54,276 INFO  🎯 EVALUATION: 1.2s since last, processing at 2025-07-09 23:45:54.276353+00:00
2025-07-10 01:45:54,276 INFO  📊 Calculating features for decision...
2025-07-10 01:45:54,276 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:45:54,277 WARNING 🔍 DUPLICATE TIMESTAMPS DETECTED in timeframe 'trades': 3 duplicates
2025-07-10 01:45:54,277 WARNING    Index before dedup: 6 rows
2025-07-10 01:45:54,277 WARNING    Index after dedup: 3 rows
2025-07-10 01:45:54,289 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:45:54,290 INFO  Calculating technical indicators...
2025-07-10 01:45:54,349 INFO  Calculating Volume Imbalance...
2025-07-10 01:45:54,350 INFO  Calculating and merging order flow features...
2025-07-10 01:45:54,352 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:54,355 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:45:54,362 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:45:54,363 INFO  Calculated HF Trade features: 3
2025-07-10 01:45:54,364 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:45:54,364 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:54,373 INFO  Calculating and merging order book features...
2025-07-10 01:45:54,376 INFO  Calculated OB features: 22
2025-07-10 01:45:54,377 WARNING Order book columns ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_ask_vol_l3', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:54,380 INFO  Merging order book features: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_ask_vol_l3', 'depth_slope5']
2025-07-10 01:45:54,395 INFO  📥 CoinAPI 1s message #48: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:54.2360000Z","time_coinapi":"2025-07-09T23:45:54.3981969Z","asks":[{"price":2.4067,"size":5405.1},{"price":2.4077,"size":16198.2},{"price":2.4079...
2025-07-10 01:45:54,395 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:54.2360000Z', 'time_coinapi': '2025-07-09T23:45:54.3981969Z', 'asks': [{'price': 2.4067, 'size': 5405.1}, {'price': 2.4077, 'size': 16198.2}, ...
2025-07-10 01:45:54,395 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:54,396 INFO  Successfully filled NaNs for merged OB columns: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_ask_vol_l3', 'depth_slope5']
2025-07-10 01:45:54,397 INFO  Calculating time-since-event features...
2025-07-10 01:45:54,523 INFO  📥 CoinAPI 1s message #49: {"time_exchange":"2025-07-09T23:45:54.3450000Z","time_coinapi":"2025-07-09T23:45:54.5083378Z","uuid":"271f9f36-e810-496e-b221-e98c42ab5b0e","price":2.4063,"size":207.7,"taker_side":"SELL","symbol_id":...
2025-07-10 01:45:54,530 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:45:54.3450000Z', 'time_coinapi': '2025-07-09T23:45:54.5083378Z', 'uuid': '271f9f36-e810-496e-b221-e98c42ab5b0e', 'price': 2.4063, 'size': 207.7, 'taker_side': 'SELL', ...
2025-07-10 01:45:54,540 INFO  📊 CoinAPI trade received for 1s: price=2.4063, size=207.7
2025-07-10 01:45:54,550 INFO  📊 1s OHLCV from trades: $2.406300 vol=442.40
2025-07-10 01:45:54,556 INFO  📥 CoinAPI 1s message #50: {"time_exchange":"2025-07-09T23:45:54.3450000Z","time_coinapi":"2025-07-09T23:45:54.5084219Z","uuid":"cfc3ce29-40c9-4204-9e80-120b54a8e1a9","price":2.4063,"size":30.8,"taker_side":"SELL","symbol_id":"...
2025-07-10 01:45:54,562 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:45:54.3450000Z', 'time_coinapi': '2025-07-09T23:45:54.5084219Z', 'uuid': 'cfc3ce29-40c9-4204-9e80-120b54a8e1a9', 'price': 2.4063, 'size': 30.8, 'taker_side': 'SELL', '...
2025-07-10 01:45:54,568 INFO  📊 CoinAPI trade received for 1s: price=2.4063, size=30.8
2025-07-10 01:45:54,606 INFO  📥 CoinAPI 1s message #51: {"time_exchange":"2025-07-09T23:45:54.3450000Z","time_coinapi":"2025-07-09T23:45:54.5084363Z","uuid":"6a9f3720-28ce-44a1-9205-a963e3a916ff","price":2.4063,"size":194.7,"taker_side":"SELL","symbol_id":...
2025-07-10 01:45:54,613 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:45:54.3450000Z', 'time_coinapi': '2025-07-09T23:45:54.5084363Z', 'uuid': '6a9f3720-28ce-44a1-9205-a963e3a916ff', 'price': 2.4063, 'size': 194.7, 'taker_side': 'SELL', ...
2025-07-10 01:45:54,619 INFO  📊 CoinAPI trade received for 1s: price=2.4063, size=194.7
2025-07-10 01:45:54,624 INFO  📥 CoinAPI 1s message #52: {"time_exchange":"2025-07-09T23:45:54.3450000Z","time_coinapi":"2025-07-09T23:45:54.5084446Z","uuid":"eb0e2d18-e34c-4178-8a62-3204d94eb36d","price":2.4063,"size":222.7,"taker_side":"SELL","symbol_id":...
2025-07-10 01:45:54,630 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:45:54.3450000Z', 'time_coinapi': '2025-07-09T23:45:54.5084446Z', 'uuid': 'eb0e2d18-e34c-4178-8a62-3204d94eb36d', 'price': 2.4063, 'size': 222.7, 'taker_side': 'SELL', ...
2025-07-10 01:45:54,636 INFO  📊 CoinAPI trade received for 1s: price=2.4063, size=222.7
2025-07-10 01:45:54,642 INFO  📥 CoinAPI 1s message #53: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:54.3750000Z","time_coinapi":"2025-07-09T23:45:54.5376954Z","asks":[{"price":2.4063,"size":20603.0},{"price":2.4064,"size":13143.8},{"price":2.406...
2025-07-10 01:45:54,657 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:54.3750000Z', 'time_coinapi': '2025-07-09T23:45:54.5376954Z', 'asks': [{'price': 2.4063, 'size': 20603.0}, {'price': 2.4064, 'size': 13143.8},...
2025-07-10 01:45:54,663 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:54,670 INFO  📥 CoinAPI 1s message #54: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:54.3750000Z","time_coinapi":"2025-07-09T23:45:54.5376954Z","asks":[{"price":2.4077,"size":22982.2},{"price":2.4079,"size":6287.1},{"price":2.4080...
2025-07-10 01:45:54,676 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:54.3750000Z', 'time_coinapi': '2025-07-09T23:45:54.5376954Z', 'asks': [{'price': 2.4077, 'size': 22982.2}, {'price': 2.4079, 'size': 6287.1}, ...
2025-07-10 01:45:54,680 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:54,680 INFO  📥 CoinAPI 1s message #55: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:54.4820000Z","time_coinapi":"2025-07-09T23:45:54.6440253Z","asks":[{"price":2.4063,"size":20609.9},{"price":2.4065,"size":2860.5},{"price":2.4066...
2025-07-10 01:45:54,684 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:54.4820000Z', 'time_coinapi': '2025-07-09T23:45:54.6440253Z', 'asks': [{'price': 2.4063, 'size': 20609.9}, {'price': 2.4065, 'size': 2860.5}, ...
2025-07-10 01:45:54,690 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:54,766 INFO  📥 CoinAPI 1s message #56: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:54.5870000Z","time_coinapi":"2025-07-09T23:45:54.7503766Z","asks":[{"price":2.4064,"size":7579.8},{"price":2.4065,"size":2867.4},{"price":2.4068,...
2025-07-10 01:45:54,773 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:54.5870000Z', 'time_coinapi': '2025-07-09T23:45:54.7503766Z', 'asks': [{'price': 2.4064, 'size': 7579.8}, {'price': 2.4065, 'size': 2867.4}, {...
2025-07-10 01:45:54,785 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:54,900 INFO  📥 CoinAPI 1s message #57: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:54.7250000Z","time_coinapi":"2025-07-09T23:45:54.8888471Z","asks":[{"price":2.4065,"size":2449.9},{"price":2.4067,"size":9082.4},{"price":2.4068,...
2025-07-10 01:45:54,915 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:54.7250000Z', 'time_coinapi': '2025-07-09T23:45:54.8888471Z', 'asks': [{'price': 2.4065, 'size': 2449.9}, {'price': 2.4067, 'size': 9082.4}, {...
2025-07-10 01:45:54,927 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:54,967 INFO  Successfully calculated time-since-event features
2025-07-10 01:45:54,967 INFO  Performing final cleanup...
2025-07-10 01:45:55,009 INFO  Final DataFrame shape: (56523, 66). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2']
2025-07-10 01:45:55,012 INFO  📊 Features calculated by indicators: 66 features
2025-07-10 01:45:55,012 INFO  📊 Base DataFrame columns: 66 columns
2025-07-10 01:45:55,012 INFO  📊 Processing features for model compatibility...
2025-07-10 01:45:55,012 INFO  🔍 DEBUG: feature_cols from config: 66 features
2025-07-10 01:45:55,042 INFO  📥 CoinAPI 1s message #58: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:54.8730000Z","time_coinapi":"2025-07-09T23:45:55.0361311Z","asks":[{"price":2.4066,"size":5623.3}],"bids":[{"price":2.4059,"size":8914.6},{"price...
2025-07-10 01:45:55,042 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:54.8730000Z', 'time_coinapi': '2025-07-09T23:45:55.0361311Z', 'asks': [{'price': 2.4066, 'size': 5623.3}], 'bids': [{'price': 2.4059, 'size': ...
2025-07-10 01:45:55,042 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:55,067 INFO  🔍 DEBUG: feature_cols after feature smoothing: 66 features
2025-07-10 01:45:55,069 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:45:55,073 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'spread': 56523, 'mid_price': 56523, 'tob_imbalance': 56523, 'depth_imbalance5': 56523, 'depth_slope5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523}
2025-07-10 01:45:55,088 INFO  🔍 DEBUG: feature_cols before base_df selection: 66 features
2025-07-10 01:45:55,098 INFO  🔍 DEBUG: feature_cols after base_df selection: 66 features
2025-07-10 01:45:55,098 INFO  📊 Feature processing diagnostics:
2025-07-10 01:45:55,098 INFO     Total features: 66
2025-07-10 01:45:55,098 INFO     DataFrame shape: (56523, 66)
2025-07-10 01:45:55,098 INFO     DataFrame columns: 66
2025-07-10 01:45:55,111 INFO  📥 CoinAPI 5m message #11: {"type":"hearbeat"}...
2025-07-10 01:45:55,111 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:55,115 INFO     close: 2.405400
2025-07-10 01:45:55,115 INFO     volume: 11.942529
2025-07-10 01:45:55,115 INFO     RSI_14: 35.795143
2025-07-10 01:45:55,115 INFO     EMA_21: 2.270887
2025-07-10 01:45:55,115 INFO     ATR_14: 0.000179
2025-07-10 01:45:55,115 INFO  ✅ Feature processing complete: 66 features, 56523 rows
2025-07-10 01:45:55,115 INFO  ✅ INDICATORS CHANGED: Hash None → 7225446112128484232
2025-07-10 01:45:55,115 INFO     close: 2.405400
2025-07-10 01:45:55,116 INFO     RSI_14: 35.795143
2025-07-10 01:45:55,116 INFO     EMA_21: 2.270887
2025-07-10 01:45:55,116 INFO     ATR_14: 0.000179
2025-07-10 01:45:55,116 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:45:55,116 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:45:55,116 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:45:55,165 INFO  📥 CoinAPI 1s message #59: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:54.9750000Z","time_coinapi":"2025-07-09T23:45:55.1391862Z","asks":[{"price":2.4068,"size":5367.8},{"price":2.4076,"size":15476.6}],"bids":[],"sym...
2025-07-10 01:45:55,166 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:54.9750000Z', 'time_coinapi': '2025-07-09T23:45:55.1391862Z', 'asks': [{'price': 2.4068, 'size': 5367.8}, {'price': 2.4076, 'size': 15476.6}],...
2025-07-10 01:45:55,166 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:55,167 INFO  ✅ Forward-fill complete: 66 features, 56523 rows
2025-07-10 01:45:55,167 INFO  🔧 State vector dimensions:
2025-07-10 01:45:55,167 INFO     Expected total obs: 1451
2025-07-10 01:45:55,167 INFO     Lookback: 30
2025-07-10 01:45:55,167 INFO     Features needed: 48
2025-07-10 01:45:55,167 INFO     Features from config: 66
2025-07-10 01:45:55,168 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:45:55,168 INFO  🔍 DEBUG: feature_cols before dimension check: 66 features
2025-07-10 01:45:55,168 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2']
2025-07-10 01:45:55,168 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:45:55,168 WARNING    Model expects 48 features but config has 66
2025-07-10 01:45:55,168 WARNING    Truncating 18 features to match model
2025-07-10 01:45:55,168 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:45:55,170 INFO  📊 Final feature selection: 48 features
2025-07-10 01:45:55,172 INFO  🤖 Model decision: action=[-0.3325231   0.26103497 -0.2871927  -0.1536051 ], price=$2.4063, pos=0
2025-07-10 01:45:55,172 INFO  🔍 Action variance (last 10): [0. 0. 0. 0.]
2025-07-10 01:45:55,172 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:45:55,172 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406300
2025-07-10 01:45:55,172 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:45:55,172 INFO  🔍 State vector stats: mean=1.362960, std=1.881554, min=-0.258819, max=5.000000
2025-07-10 01:45:55,172 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:45:55,173 INFO  🔍 Normalized obs stats: mean=-2.367448, std=3.742415
2025-07-10 01:45:55,173 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4063]
2025-07-10 01:45:55,173 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:45:55,173 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:45:55,173 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:45:55,173 INFO  🔍 Feature variance - Close: 0.00000654, RSI: 0.00000000
2025-07-10 01:45:55,173 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:45:55,173 INFO  🔍 Current timestamp: 2025-07-09 23:45:55.116912+00:00
2025-07-10 01:45:55,173 INFO  🔍 1s mode: decision_ts=2025-07-09 23:45:51+00:00, current_ts=2025-07-09 23:45:55.116912+00:00
2025-07-10 01:45:55,174 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:45:51+00:00
2025-07-10 01:45:55,174 INFO  2025-07-09 23:45:55.174224+00:00: New day, reset daily limits
2025-07-10 01:45:55,174 INFO  🎯 ENTRY CHECK: entry_sig=-0.332523, long_thr=0.7, short_thr=0.7, stable=True
2025-07-10 01:45:55,254 INFO  📥 CoinAPI 1s message #60: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:55.0820000Z","time_coinapi":"2025-07-09T23:45:55.2456382Z","asks":[{"price":2.4067,"size":9082.3},{"price":2.4068,"size":11673.8}],"bids":[{"pric...
2025-07-10 01:45:55,255 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:55.0820000Z', 'time_coinapi': '2025-07-09T23:45:55.2456382Z', 'asks': [{'price': 2.4067, 'size': 9082.3}, {'price': 2.4068, 'size': 11673.8}],...
2025-07-10 01:45:55,255 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:55,359 INFO  📥 CoinAPI 1s message #61: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:55.1860000Z","time_coinapi":"2025-07-09T23:45:55.3482597Z","asks":[],"bids":[{"price":2.4059,"size":8497.1},{"price":1.9250,"size":118.5}],"symbo...
2025-07-10 01:45:55,359 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:55.1860000Z', 'time_coinapi': '2025-07-09T23:45:55.3482597Z', 'asks': [], 'bids': [{'price': 2.4059, 'size': 8497.1}, {'price': 1.925, 'size':...
2025-07-10 01:45:55,360 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:55,361 INFO  🎯 EVALUATION: 1.1s since last, processing at 2025-07-09 23:45:55.361640+00:00
2025-07-10 01:45:55,361 INFO  📊 Calculating features for decision...
2025-07-10 01:45:55,361 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:45:55,362 WARNING 🔍 DUPLICATE TIMESTAMPS DETECTED in timeframe 'trades': 3 duplicates
2025-07-10 01:45:55,362 WARNING    Index before dedup: 8 rows
2025-07-10 01:45:55,362 WARNING    Index after dedup: 5 rows
2025-07-10 01:45:55,374 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:45:55,374 INFO  Calculating technical indicators...
2025-07-10 01:45:55,427 INFO  Calculating Volume Imbalance...
2025-07-10 01:45:55,428 INFO  Calculating and merging order flow features...
2025-07-10 01:45:55,430 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:55,433 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:45:55,441 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:45:55,442 INFO  Calculated HF Trade features: 3
2025-07-10 01:45:55,443 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:45:55,443 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:55,453 INFO  Calculating and merging order book features...
2025-07-10 01:45:55,456 INFO  Calculated OB features: 22
2025-07-10 01:45:55,457 WARNING Order book columns ['depth_imbalance5', 'ob_bid_vol_l1'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:55,460 INFO  Merging order book features: ['depth_imbalance5', 'ob_bid_vol_l1']
2025-07-10 01:45:55,469 INFO  Successfully filled NaNs for merged OB columns: ['depth_imbalance5', 'ob_bid_vol_l1']
2025-07-10 01:45:55,469 INFO  Calculating time-since-event features...
2025-07-10 01:45:55,800 INFO  📥 CoinAPI 1s message #62: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:55.5980000Z","time_coinapi":"2025-07-09T23:45:55.7612210Z","asks":[{"price":2.4063,"size":17258.3},{"price":2.4064,"size":7479.8},{"price":2.4101...
2025-07-10 01:45:55,806 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:55.5980000Z', 'time_coinapi': '2025-07-09T23:45:55.7612210Z', 'asks': [{'price': 2.4063, 'size': 17258.3}, {'price': 2.4064, 'size': 7479.8}, ...
2025-07-10 01:45:55,812 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:55,918 INFO  📥 CoinAPI 1s message #63: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:55.7160000Z","time_coinapi":"2025-07-09T23:45:55.8788832Z","asks":[],"bids":[{"price":1.9250,"size":118.5}],"symbol_id":"BINANCEFTS_PERP_XRP_USDC...
2025-07-10 01:45:55,924 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:55.7160000Z', 'time_coinapi': '2025-07-09T23:45:55.8788832Z', 'asks': [], 'bids': [{'price': 1.925, 'size': 118.5}], 'symbol_id': 'BINANCEFTS_...
2025-07-10 01:45:55,924 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:56,058 INFO  Successfully calculated time-since-event features
2025-07-10 01:45:56,058 INFO  Performing final cleanup...
2025-07-10 01:45:56,101 INFO  Final DataFrame shape: (56523, 66). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2']
2025-07-10 01:45:56,103 INFO  📊 Features calculated by indicators: 66 features
2025-07-10 01:45:56,103 INFO  📊 Base DataFrame columns: 66 columns
2025-07-10 01:45:56,103 INFO  📊 Processing features for model compatibility...
2025-07-10 01:45:56,103 INFO  🔍 DEBUG: feature_cols from config: 66 features
2025-07-10 01:45:56,111 INFO  📥 CoinAPI 5m message #12: {"type":"hearbeat"}...
2025-07-10 01:45:56,111 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:56,121 INFO  📥 CoinAPI 1s message #64: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:55.9640000Z","time_coinapi":"2025-07-09T23:45:56.1267487Z","asks":[{"price":2.4063,"size":14181.7},{"price":2.4066,"size":4986.5},{"price":2.4067...
2025-07-10 01:45:56,121 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:55.9640000Z', 'time_coinapi': '2025-07-09T23:45:56.1267487Z', 'asks': [{'price': 2.4063, 'size': 14181.7}, {'price': 2.4066, 'size': 4986.5}, ...
2025-07-10 01:45:56,121 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:56,122 INFO  📥 CoinAPI 1s message #65: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:55.9640000Z","time_coinapi":"2025-07-09T23:45:56.1267487Z","asks":[{"price":2.4100,"size":8262.9},{"price":2.4101,"size":3642.4},{"price":2.4103,...
2025-07-10 01:45:56,122 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:55.9640000Z', 'time_coinapi': '2025-07-09T23:45:56.1267487Z', 'asks': [{'price': 2.41, 'size': 8262.9}, {'price': 2.4101, 'size': 3642.4}, {'p...
2025-07-10 01:45:56,122 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:56,160 INFO  🔍 DEBUG: feature_cols after feature smoothing: 66 features
2025-07-10 01:45:56,162 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:45:56,166 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'depth_imbalance5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523}
2025-07-10 01:45:56,177 INFO  🔍 DEBUG: feature_cols before base_df selection: 66 features
2025-07-10 01:45:56,187 INFO  🔍 DEBUG: feature_cols after base_df selection: 66 features
2025-07-10 01:45:56,187 INFO  📊 Feature processing diagnostics:
2025-07-10 01:45:56,187 INFO     Total features: 66
2025-07-10 01:45:56,187 INFO     DataFrame shape: (56523, 66)
2025-07-10 01:45:56,187 INFO     DataFrame columns: 66
2025-07-10 01:45:56,207 INFO     close: 2.405400
2025-07-10 01:45:56,207 INFO     volume: 11.942529
2025-07-10 01:45:56,207 INFO     RSI_14: 35.795143
2025-07-10 01:45:56,207 INFO     EMA_21: 2.270887
2025-07-10 01:45:56,207 INFO     ATR_14: 0.000179
2025-07-10 01:45:56,207 INFO  ✅ Feature processing complete: 66 features, 56523 rows
2025-07-10 01:45:56,207 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:45:56,207 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:45:56,208 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:45:56,208 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:45:56,208 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:45:56,258 INFO  📥 CoinAPI 1s message #66: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:56.0760000Z","time_coinapi":"2025-07-09T23:45:56.2382229Z","asks":[{"price":2.4081,"size":17311.7}],"bids":[{"price":2.4062,"size":7505.9}],"symb...
2025-07-10 01:45:56,258 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:56.0760000Z', 'time_coinapi': '2025-07-09T23:45:56.2382229Z', 'asks': [{'price': 2.4081, 'size': 17311.7}], 'bids': [{'price': 2.4062, 'size':...
2025-07-10 01:45:56,258 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:56,259 INFO  ✅ Forward-fill complete: 66 features, 56523 rows
2025-07-10 01:45:56,260 INFO  🔧 State vector dimensions:
2025-07-10 01:45:56,260 INFO     Expected total obs: 1451
2025-07-10 01:45:56,260 INFO     Lookback: 30
2025-07-10 01:45:56,260 INFO     Features needed: 48
2025-07-10 01:45:56,260 INFO     Features from config: 66
2025-07-10 01:45:56,260 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:45:56,261 INFO  🔍 DEBUG: feature_cols before dimension check: 66 features
2025-07-10 01:45:56,261 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2']
2025-07-10 01:45:56,261 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:45:56,261 WARNING    Model expects 48 features but config has 66
2025-07-10 01:45:56,261 WARNING    Truncating 18 features to match model
2025-07-10 01:45:56,261 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:45:56,263 INFO  📊 Final feature selection: 48 features
2025-07-10 01:45:56,264 INFO  🤖 Model decision: action=[-0.12638998  0.970541   -0.4786291  -0.14375275], price=$2.4063, pos=0
2025-07-10 01:45:56,265 INFO  🔍 Action variance (last 10): [1.06227165e-02 1.25849709e-01 9.16197430e-03 2.42671995e-05]
2025-07-10 01:45:56,265 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:45:56,265 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406300
2025-07-10 01:45:56,265 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:45:56,265 INFO  🔍 State vector stats: mean=1.409904, std=1.875275, min=-0.258819, max=5.000000
2025-07-10 01:45:56,265 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:45:56,265 INFO  🔍 Normalized obs stats: mean=-2.228778, std=3.674745
2025-07-10 01:45:56,265 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4063]
2025-07-10 01:45:56,266 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:45:56,266 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:45:56,266 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:45:56,266 INFO  🔍 Feature variance - Close: 0.00000654, RSI: 0.00000000
2025-07-10 01:45:56,266 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:45:56,266 INFO  🔍 Current timestamp: 2025-07-09 23:45:56.208682+00:00
2025-07-10 01:45:56,266 INFO  🔍 1s mode: decision_ts=2025-07-09 23:45:53+00:00, current_ts=2025-07-09 23:45:56.208682+00:00
2025-07-10 01:45:56,266 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:45:53+00:00
2025-07-10 01:45:56,356 INFO  📥 CoinAPI 1s message #67: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:56.1970000Z","time_coinapi":"2025-07-09T23:45:56.3604713Z","asks":[],"bids":[{"price":2.4062,"size":8004.5}],"symbol_id":"BINANCEFTS_PERP_XRP_USD...
2025-07-10 01:45:56,356 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:56.1970000Z', 'time_coinapi': '2025-07-09T23:45:56.3604713Z', 'asks': [], 'bids': [{'price': 2.4062, 'size': 8004.5}], 'symbol_id': 'BINANCEFT...
2025-07-10 01:45:56,356 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:56,647 INFO  📥 CoinAPI 1s message #68: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:56.4350000Z","time_coinapi":"2025-07-09T23:45:56.5988191Z","asks":[],"bids":[{"price":2.4048,"size":12923.3},{"price":2.4047,"size":17622.3},{"pr...
2025-07-10 01:45:56,648 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:56.4350000Z', 'time_coinapi': '2025-07-09T23:45:56.5988191Z', 'asks': [], 'bids': [{'price': 2.4048, 'size': 12923.3}, {'price': 2.4047, 'size...
2025-07-10 01:45:56,648 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:56,650 INFO  🎯 EVALUATION: 1.3s since last, processing at 2025-07-09 23:45:56.650542+00:00
2025-07-10 01:45:56,650 INFO  📊 Calculating features for decision...
2025-07-10 01:45:56,650 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:45:56,662 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:45:56,665 INFO  Calculating technical indicators...
2025-07-10 01:45:56,721 INFO  Calculating Volume Imbalance...
2025-07-10 01:45:56,722 INFO  Calculating and merging order flow features...
2025-07-10 01:45:56,723 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:56,726 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:45:56,732 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:45:56,734 INFO  Calculated HF Trade features: 3
2025-07-10 01:45:56,735 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:45:56,735 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:56,745 INFO  Calculating and merging order book features...
2025-07-10 01:45:56,748 INFO  Calculated OB features: 22
2025-07-10 01:45:56,749 WARNING Order book columns ['depth_imbalance5', 'ob_bid_vol_l1', 'ob_bid_vol_l2', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:56,752 INFO  Merging order book features: ['depth_imbalance5', 'ob_bid_vol_l1', 'ob_bid_vol_l2', 'depth_slope5']
2025-07-10 01:45:56,761 INFO  Successfully filled NaNs for merged OB columns: ['depth_imbalance5', 'ob_bid_vol_l1', 'ob_bid_vol_l2', 'depth_slope5']
2025-07-10 01:45:56,761 INFO  📥 CoinAPI 1s message #69: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:56.5900000Z","time_coinapi":"2025-07-09T23:45:56.7532906Z","asks":[{"price":2.4102,"size":86632.6},{"price":2.4103,"size":157463.8}],"bids":[{"pr...
2025-07-10 01:45:56,761 INFO  Calculating time-since-event features...
2025-07-10 01:45:56,761 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:56.5900000Z', 'time_coinapi': '2025-07-09T23:45:56.7532906Z', 'asks': [{'price': 2.4102, 'size': 86632.6}, {'price': 2.4103, 'size': 157463.8}...
2025-07-10 01:45:56,761 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:56,918 INFO  📥 CoinAPI 1s message #70: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:56.7150000Z","time_coinapi":"2025-07-09T23:45:56.8783553Z","asks":[],"bids":[{"price":2.4056,"size":13217.5},{"price":2.4054,"size":8157.8},{"pri...
2025-07-10 01:45:56,924 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:56.7150000Z', 'time_coinapi': '2025-07-09T23:45:56.8783553Z', 'asks': [], 'bids': [{'price': 2.4056, 'size': 13217.5}, {'price': 2.4054, 'size...
2025-07-10 01:45:56,930 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:57,097 INFO  📥 CoinAPI 1s message #71: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:56.8970000Z","time_coinapi":"2025-07-09T23:45:57.0597955Z","asks":[{"price":2.4063,"size":10663.1},{"price":2.4101,"size":12466.9}],"bids":[{"pri...
2025-07-10 01:45:57,104 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:56.8970000Z', 'time_coinapi': '2025-07-09T23:45:57.0597955Z', 'asks': [{'price': 2.4063, 'size': 10663.1}, {'price': 2.4101, 'size': 12466.9}]...
2025-07-10 01:45:57,110 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:57,123 INFO  📥 CoinAPI 5m message #13: {"type":"hearbeat"}...
2025-07-10 01:45:57,140 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:57,212 INFO  📥 CoinAPI 1s message #72: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:57.0040000Z","time_coinapi":"2025-07-09T23:45:57.1675282Z","asks":[],"bids":[{"price":2.4031,"size":9056.8},{"price":2.4030,"size":61738.7},{"pri...
2025-07-10 01:45:57,218 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:57.0040000Z', 'time_coinapi': '2025-07-09T23:45:57.1675282Z', 'asks': [], 'bids': [{'price': 2.4031, 'size': 9056.8}, {'price': 2.403, 'size':...
2025-07-10 01:45:57,225 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:57,315 INFO  Successfully calculated time-since-event features
2025-07-10 01:45:57,315 INFO  Performing final cleanup...
2025-07-10 01:45:57,360 INFO  Final DataFrame shape: (56523, 66). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2']
2025-07-10 01:45:57,362 INFO  📊 Features calculated by indicators: 66 features
2025-07-10 01:45:57,363 INFO  📊 Base DataFrame columns: 66 columns
2025-07-10 01:45:57,363 INFO  📊 Processing features for model compatibility...
2025-07-10 01:45:57,363 INFO  🔍 DEBUG: feature_cols from config: 66 features
2025-07-10 01:45:57,391 INFO  📥 CoinAPI 1s message #73: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:57.2280000Z","time_coinapi":"2025-07-09T23:45:57.3906839Z","asks":[],"bids":[{"price":2.4062,"size":8024.9}],"symbol_id":"BINANCEFTS_PERP_XRP_USD...
2025-07-10 01:45:57,391 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:57.2280000Z', 'time_coinapi': '2025-07-09T23:45:57.3906839Z', 'asks': [], 'bids': [{'price': 2.4062, 'size': 8024.9}], 'symbol_id': 'BINANCEFT...
2025-07-10 01:45:57,391 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:57,422 INFO  🔍 DEBUG: feature_cols after feature smoothing: 66 features
2025-07-10 01:45:57,425 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:45:57,430 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'depth_imbalance5': 56523, 'depth_slope5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523}
2025-07-10 01:45:57,447 INFO  🔍 DEBUG: feature_cols before base_df selection: 66 features
2025-07-10 01:45:57,461 INFO  🔍 DEBUG: feature_cols after base_df selection: 66 features
2025-07-10 01:45:57,461 INFO  📊 Feature processing diagnostics:
2025-07-10 01:45:57,461 INFO     Total features: 66
2025-07-10 01:45:57,461 INFO     DataFrame shape: (56523, 66)
2025-07-10 01:45:57,461 INFO     DataFrame columns: 66
2025-07-10 01:45:57,478 INFO     close: 2.405400
2025-07-10 01:45:57,478 INFO     volume: 11.942529
2025-07-10 01:45:57,478 INFO     RSI_14: 35.795143
2025-07-10 01:45:57,478 INFO     EMA_21: 2.270887
2025-07-10 01:45:57,478 INFO     ATR_14: 0.000179
2025-07-10 01:45:57,478 INFO  ✅ Feature processing complete: 66 features, 56523 rows
2025-07-10 01:45:57,478 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:45:57,478 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:45:57,479 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:45:57,479 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:45:57,479 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:45:57,528 INFO  ✅ Forward-fill complete: 66 features, 56523 rows
2025-07-10 01:45:57,528 INFO  🔧 State vector dimensions:
2025-07-10 01:45:57,528 INFO     Expected total obs: 1451
2025-07-10 01:45:57,528 INFO     Lookback: 30
2025-07-10 01:45:57,528 INFO     Features needed: 48
2025-07-10 01:45:57,528 INFO     Features from config: 66
2025-07-10 01:45:57,528 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:45:57,528 INFO  🔍 DEBUG: feature_cols before dimension check: 66 features
2025-07-10 01:45:57,528 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2']
2025-07-10 01:45:57,528 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:45:57,528 WARNING    Model expects 48 features but config has 66
2025-07-10 01:45:57,529 WARNING    Truncating 18 features to match model
2025-07-10 01:45:57,529 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:45:57,530 INFO  📊 Final feature selection: 48 features
2025-07-10 01:45:57,531 INFO  🤖 Model decision: action=[-0.9479881  -0.57564723  0.23768187 -0.28609967], price=$2.4063, pos=0
2025-07-10 01:45:57,531 INFO  🔍 Action variance (last 10): [0.12181238 0.39934823 0.09169363 0.00421272]
2025-07-10 01:45:57,531 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:45:57,531 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406300
2025-07-10 01:45:57,531 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:45:57,531 INFO  🔍 State vector stats: mean=1.409904, std=1.875275, min=-0.258819, max=5.000000
2025-07-10 01:45:57,531 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:45:57,532 INFO  🔍 Normalized obs stats: mean=-2.228778, std=3.674745
2025-07-10 01:45:57,532 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4063]
2025-07-10 01:45:57,532 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:45:57,532 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:45:57,532 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:45:57,532 INFO  🔍 Feature variance - Close: 0.00000654, RSI: 0.00000000
2025-07-10 01:45:57,532 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:45:57,532 INFO  🔍 Current timestamp: 2025-07-09 23:45:57.479361+00:00
2025-07-10 01:45:57,532 INFO  🔍 1s mode: decision_ts=2025-07-09 23:45:53+00:00, current_ts=2025-07-09 23:45:57.479361+00:00
2025-07-10 01:45:57,532 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:45:53+00:00
2025-07-10 01:45:57,923 INFO  📥 CoinAPI 1s message #74: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:57.7490000Z","time_coinapi":"2025-07-09T23:45:57.9124797Z","asks":[{"price":2.4063,"size":6540.5},{"price":2.4064,"size":7279.8},{"price":2.4065,...
2025-07-10 01:45:57,924 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:57.7490000Z', 'time_coinapi': '2025-07-09T23:45:57.9124797Z', 'asks': [{'price': 2.4063, 'size': 6540.5}, {'price': 2.4064, 'size': 7279.8}, {...
2025-07-10 01:45:57,924 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:57,929 INFO  🎯 EVALUATION: 1.3s since last, processing at 2025-07-09 23:45:57.928965+00:00
2025-07-10 01:45:57,929 INFO  📊 Calculating features for decision...
2025-07-10 01:45:57,929 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:45:57,943 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:45:57,944 INFO  Calculating technical indicators...
2025-07-10 01:45:58,007 INFO  Calculating Volume Imbalance...
2025-07-10 01:45:58,008 INFO  Calculating and merging order flow features...
2025-07-10 01:45:58,010 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:58,013 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:45:58,020 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:45:58,021 INFO  Calculated HF Trade features: 3
2025-07-10 01:45:58,022 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:45:58,022 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:58,029 INFO  📥 CoinAPI 1s message #75: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:57.8590000Z","time_coinapi":"2025-07-09T23:45:58.0233384Z","asks":[{"price":2.4063,"size":6990.7},{"price":2.4064,"size":7888.4}],"bids":[{"price...
2025-07-10 01:45:58,030 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:57.8590000Z', 'time_coinapi': '2025-07-09T23:45:58.0233384Z', 'asks': [{'price': 2.4063, 'size': 6990.7}, {'price': 2.4064, 'size': 7888.4}], ...
2025-07-10 01:45:58,030 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:58,033 INFO  Calculating and merging order book features...
2025-07-10 01:45:58,036 INFO  Calculated OB features: 22
2025-07-10 01:45:58,037 WARNING Order book columns ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_bid_vol_l4', 'ob_bid_vol_l5', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:58,040 INFO  Merging order book features: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'depth_slope5']
2025-07-10 01:45:58,058 INFO  Successfully filled NaNs for merged OB columns: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'depth_slope5']
2025-07-10 01:45:58,058 INFO  Calculating time-since-event features...
2025-07-10 01:45:58,142 INFO  📥 CoinAPI 5m message #14: {"type":"hearbeat"}...
2025-07-10 01:45:58,154 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:58,190 INFO  📥 CoinAPI 1s message #76: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:58.0080000Z","time_coinapi":"2025-07-09T23:45:58.1711344Z","asks":[],"bids":[{"price":2.4062,"size":10253.0},{"price":2.4060,"size":5436.7},{"pri...
2025-07-10 01:45:58,195 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:58.0080000Z', 'time_coinapi': '2025-07-09T23:45:58.1711344Z', 'asks': [], 'bids': [{'price': 2.4062, 'size': 10253.0}, {'price': 2.406, 'size'...
2025-07-10 01:45:58,196 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:58,314 INFO  📥 CoinAPI 1s message #77: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:58.1160000Z","time_coinapi":"2025-07-09T23:45:58.2796040Z","asks":[{"price":2.4077,"size":22935.2},{"price":2.4079,"size":6334.1},{"price":2.4126...
2025-07-10 01:45:58,320 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:58.1160000Z', 'time_coinapi': '2025-07-09T23:45:58.2796040Z', 'asks': [{'price': 2.4077, 'size': 22935.2}, {'price': 2.4079, 'size': 6334.1}, ...
2025-07-10 01:45:58,325 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:58,419 INFO  📥 CoinAPI 1s message #78: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:58.2350000Z","time_coinapi":"2025-07-09T23:45:58.3985258Z","asks":[],"bids":[{"price":2.4062,"size":10263.1},{"price":2.4059,"size":8497.1},{"pri...
2025-07-10 01:45:58,425 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:58.2350000Z', 'time_coinapi': '2025-07-09T23:45:58.3985258Z', 'asks': [], 'bids': [{'price': 2.4062, 'size': 10263.1}, {'price': 2.4059, 'size...
2025-07-10 01:45:58,442 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:58,567 INFO  📥 CoinAPI 1s message #79: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:58.3820000Z","time_coinapi":"2025-07-09T23:45:58.5446412Z","asks":[{"price":2.4077,"size":22982.2},{"price":2.4079,"size":6287.1}],"bids":[{"pric...
2025-07-10 01:45:58,584 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:58.3820000Z', 'time_coinapi': '2025-07-09T23:45:58.5446412Z', 'asks': [{'price': 2.4077, 'size': 22982.2}, {'price': 2.4079, 'size': 6287.1}],...
2025-07-10 01:45:58,591 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:58,613 INFO  Successfully calculated time-since-event features
2025-07-10 01:45:58,613 INFO  Performing final cleanup...
2025-07-10 01:45:58,651 INFO  📥 CoinAPI 1s message #80: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:58.4840000Z","time_coinapi":"2025-07-09T23:45:58.6470527Z","asks":[],"bids":[{"price":2.4056,"size":13217.5},{"price":2.4054,"size":8157.8}],"sym...
2025-07-10 01:45:58,652 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:58.4840000Z', 'time_coinapi': '2025-07-09T23:45:58.6470527Z', 'asks': [], 'bids': [{'price': 2.4056, 'size': 13217.5}, {'price': 2.4054, 'size...
2025-07-10 01:45:58,652 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:58,656 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:45:58,659 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:45:58,659 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:45:58,659 INFO  📊 Processing features for model compatibility...
2025-07-10 01:45:58,659 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:45:58,713 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:45:58,715 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:45:58,718 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'spread': 56523, 'mid_price': 56523, 'tob_imbalance': 56523, 'depth_imbalance5': 56523, 'depth_slope5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:45:58,731 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:45:58,741 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:45:58,742 INFO  📊 Feature processing diagnostics:
2025-07-10 01:45:58,742 INFO     Total features: 69
2025-07-10 01:45:58,742 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:45:58,742 INFO     DataFrame columns: 69
2025-07-10 01:45:58,760 INFO     close: 2.405400
2025-07-10 01:45:58,760 INFO     volume: 11.942529
2025-07-10 01:45:58,760 INFO     RSI_14: 35.795143
2025-07-10 01:45:58,760 INFO     EMA_21: 2.270887
2025-07-10 01:45:58,760 INFO     ATR_14: 0.000179
2025-07-10 01:45:58,760 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:45:58,760 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:45:58,760 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:45:58,761 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:45:58,761 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:45:58,761 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:45:58,808 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:45:58,809 INFO  🔧 State vector dimensions:
2025-07-10 01:45:58,809 INFO     Expected total obs: 1451
2025-07-10 01:45:58,809 INFO     Lookback: 30
2025-07-10 01:45:58,809 INFO     Features needed: 48
2025-07-10 01:45:58,809 INFO     Features from config: 69
2025-07-10 01:45:58,809 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:45:58,809 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:45:58,809 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:45:58,809 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:45:58,809 WARNING    Model expects 48 features but config has 69
2025-07-10 01:45:58,809 WARNING    Truncating 21 features to match model
2025-07-10 01:45:58,809 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:45:58,811 INFO  📊 Final feature selection: 48 features
2025-07-10 01:45:58,813 INFO  🤖 Model decision: action=[-0.18320185 -0.03327352  0.24765253  0.68748367], price=$2.4063, pos=0
2025-07-10 01:45:58,813 INFO  🔍 Action variance (last 10): [0.10667087 0.31141028 0.1024304  0.14901021]
2025-07-10 01:45:58,813 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:45:58,813 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406300
2025-07-10 01:45:58,813 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:45:58,813 INFO  🔍 State vector stats: mean=1.362960, std=1.881555, min=-0.258819, max=5.000000
2025-07-10 01:45:58,813 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:45:58,813 INFO  🔍 Normalized obs stats: mean=-2.367447, std=3.742415
2025-07-10 01:45:58,814 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4063]
2025-07-10 01:45:58,814 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:45:58,814 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:45:58,814 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:45:58,814 INFO  🔍 Feature variance - Close: 0.00000654, RSI: 0.00000000
2025-07-10 01:45:58,814 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:45:58,814 INFO  🔍 Current timestamp: 2025-07-09 23:45:58.761564+00:00
2025-07-10 01:45:58,815 INFO  🔍 1s mode: decision_ts=2025-07-09 23:45:53+00:00, current_ts=2025-07-09 23:45:58.761564+00:00
2025-07-10 01:45:58,815 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:45:53+00:00
2025-07-10 01:45:59,037 INFO  📥 CoinAPI 1s message #81: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:58.8650000Z","time_coinapi":"2025-07-09T23:45:59.0271435Z","asks":[{"price":2.4063,"size":6990.7}],"bids":[{"price":2.4062,"size":10263.1},{"pric...
2025-07-10 01:45:59,037 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:58.8650000Z', 'time_coinapi': '2025-07-09T23:45:59.0271435Z', 'asks': [{'price': 2.4063, 'size': 6990.7}], 'bids': [{'price': 2.4062, 'size': ...
2025-07-10 01:45:59,037 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:59,038 INFO  🎯 EVALUATION: 1.1s since last, processing at 2025-07-09 23:45:59.038558+00:00
2025-07-10 01:45:59,038 INFO  📊 Calculating features for decision...
2025-07-10 01:45:59,038 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:45:59,042 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:45:59,043 INFO  Calculating technical indicators...
2025-07-10 01:45:59,092 INFO  Calculating Volume Imbalance...
2025-07-10 01:45:59,093 INFO  Calculating and merging order flow features...
2025-07-10 01:45:59,095 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:59,098 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:45:59,106 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:45:59,108 INFO  Calculated HF Trade features: 3
2025-07-10 01:45:59,108 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:45:59,108 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:59,111 INFO  📥 CoinAPI 5m message #15: {"type":"hearbeat"}...
2025-07-10 01:45:59,111 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:45:59,118 INFO  Calculating and merging order book features...
2025-07-10 01:45:59,122 INFO  Calculated OB features: 22
2025-07-10 01:45:59,123 WARNING Order book columns ['depth_imbalance5', 'ob_bid_vol_l1', 'ob_bid_vol_l2', 'ob_bid_vol_l3', 'ob_bid_vol_l4', 'ob_bid_vol_l5', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:45:59,126 INFO  Merging order book features: ['depth_imbalance5', 'ob_bid_vol_l1', 'ob_bid_vol_l2', 'ob_bid_vol_l3', 'ob_bid_vol_l4', 'ob_bid_vol_l5', 'depth_slope5']
2025-07-10 01:45:59,140 INFO  Successfully filled NaNs for merged OB columns: ['depth_imbalance5', 'ob_bid_vol_l1', 'ob_bid_vol_l2', 'ob_bid_vol_l3', 'ob_bid_vol_l4', 'ob_bid_vol_l5', 'depth_slope5']
2025-07-10 01:45:59,140 INFO  Calculating time-since-event features...
2025-07-10 01:45:59,143 INFO  📥 CoinAPI 1s message #82: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:58.9700000Z","time_coinapi":"2025-07-09T23:45:59.1336363Z","asks":[{"price":2.4063,"size":9218.8}],"bids":[{"price":2.4062,"size":8035.0},{"price...
2025-07-10 01:45:59,143 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:58.9700000Z', 'time_coinapi': '2025-07-09T23:45:59.1336363Z', 'asks': [{'price': 2.4063, 'size': 9218.8}], 'bids': [{'price': 2.4062, 'size': ...
2025-07-10 01:45:59,143 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:59,268 INFO  📥 CoinAPI 1s message #83: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:59.0800000Z","time_coinapi":"2025-07-09T23:45:59.2428945Z","asks":[],"bids":[{"price":2.4030,"size":60348.2},{"price":2.4029,"size":6641.8},{"pri...
2025-07-10 01:45:59,274 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:59.0800000Z', 'time_coinapi': '2025-07-09T23:45:59.2428945Z', 'asks': [], 'bids': [{'price': 2.403, 'size': 60348.2}, {'price': 2.4029, 'size'...
2025-07-10 01:45:59,274 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:59,697 INFO  Successfully calculated time-since-event features
2025-07-10 01:45:59,697 INFO  Performing final cleanup...
2025-07-10 01:45:59,740 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:45:59,742 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:45:59,742 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:45:59,742 INFO  📊 Processing features for model compatibility...
2025-07-10 01:45:59,742 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:45:59,797 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:45:59,799 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:45:59,804 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'depth_imbalance5': 56523, 'depth_slope5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:45:59,922 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:45:59,924 INFO  📥 CoinAPI 1s message #84: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:59.6870000Z","time_coinapi":"2025-07-09T23:45:59.8500932Z","asks":[{"price":2.4077,"size":22982.2},{"price":2.4079,"size":6287.1}],"bids":[{"pric...
2025-07-10 01:45:59,924 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:59.6870000Z', 'time_coinapi': '2025-07-09T23:45:59.8500932Z', 'asks': [{'price': 2.4077, 'size': 22982.2}, {'price': 2.4079, 'size': 6287.1}],...
2025-07-10 01:45:59,925 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:45:59,932 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:45:59,932 INFO  📊 Feature processing diagnostics:
2025-07-10 01:45:59,932 INFO     Total features: 69
2025-07-10 01:45:59,932 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:45:59,932 INFO     DataFrame columns: 69
2025-07-10 01:45:59,950 INFO     close: 2.405400
2025-07-10 01:45:59,950 INFO     volume: 11.942529
2025-07-10 01:45:59,950 INFO     RSI_14: 35.795143
2025-07-10 01:45:59,950 INFO     EMA_21: 2.270887
2025-07-10 01:45:59,950 INFO     ATR_14: 0.000179
2025-07-10 01:45:59,950 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:45:59,950 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:45:59,950 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:45:59,951 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:45:59,951 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:45:59,951 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:45:59,999 INFO  📥 CoinAPI 1s message #85: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:59.7990000Z","time_coinapi":"2025-07-09T23:45:59.9626611Z","asks":[{"price":2.4077,"size":21135.2},{"price":2.4079,"size":8134.1}],"bids":[{"pric...
2025-07-10 01:45:59,999 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:59.7990000Z', 'time_coinapi': '2025-07-09T23:45:59.9626611Z', 'asks': [{'price': 2.4077, 'size': 21135.2}, {'price': 2.4079, 'size': 8134.1}],...
2025-07-10 01:45:59,999 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:00,001 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:00,001 INFO  🔧 State vector dimensions:
2025-07-10 01:46:00,001 INFO     Expected total obs: 1451
2025-07-10 01:46:00,001 INFO     Lookback: 30
2025-07-10 01:46:00,001 INFO     Features needed: 48
2025-07-10 01:46:00,002 INFO     Features from config: 69
2025-07-10 01:46:00,002 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:00,002 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:00,002 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:00,002 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:00,002 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:00,002 WARNING    Truncating 21 features to match model
2025-07-10 01:46:00,002 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:00,004 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:00,005 INFO  🤖 Model decision: action=[ 0.8566742  -0.76979744 -0.51173365 -0.5664684 ], price=$2.4063, pos=0
2025-07-10 01:46:00,006 INFO  🔍 Action variance (last 10): [0.3370195  0.38616475 0.11314768 0.17537242]
2025-07-10 01:46:00,006 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:00,006 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406300
2025-07-10 01:46:00,006 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:00,006 INFO  🔍 State vector stats: mean=1.409904, std=1.875275, min=-0.258819, max=5.000000
2025-07-10 01:46:00,006 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:00,006 INFO  🔍 Normalized obs stats: mean=-2.228778, std=3.674745
2025-07-10 01:46:00,006 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4063]
2025-07-10 01:46:00,007 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:00,007 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:00,007 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:00,007 INFO  🔍 Feature variance - Close: 0.00000654, RSI: 0.00000000
2025-07-10 01:46:00,007 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:00,007 INFO  🔍 Current timestamp: 2025-07-09 23:45:59.951767+00:00
2025-07-10 01:46:00,007 INFO  🔍 1s mode: decision_ts=2025-07-09 23:45:53+00:00, current_ts=2025-07-09 23:45:59.951767+00:00
2025-07-10 01:46:00,007 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:45:53+00:00
2025-07-10 01:46:00,077 INFO  📥 CoinAPI 1s message #86: {"is_snapshot":false,"time_exchange":"2025-07-09T23:45:59.9090000Z","time_coinapi":"2025-07-09T23:46:00.0718672Z","asks":[],"bids":[{"price":2.4030,"size":59626.7},{"price":2.4027,"size":2835.1},{"pri...
2025-07-10 01:46:00,077 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:45:59.9090000Z', 'time_coinapi': '2025-07-09T23:46:00.0718672Z', 'asks': [], 'bids': [{'price': 2.403, 'size': 59626.7}, {'price': 2.4027, 'size'...
2025-07-10 01:46:00,077 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:00,078 INFO  🎯 EVALUATION: 1.0s since last, processing at 2025-07-09 23:46:00.078205+00:00
2025-07-10 01:46:00,078 INFO  📊 Calculating features for decision...
2025-07-10 01:46:00,078 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:00,086 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:00,086 INFO  Calculating technical indicators...
2025-07-10 01:46:00,135 INFO  📥 CoinAPI 5m message #16: {"type":"hearbeat"}...
2025-07-10 01:46:00,135 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:00,141 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:00,141 INFO  Calculating and merging order flow features...
2025-07-10 01:46:00,144 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:00,149 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:00,160 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:00,162 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:00,162 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:00,163 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:00,175 INFO  Calculating and merging order book features...
2025-07-10 01:46:00,178 INFO  Calculated OB features: 22
2025-07-10 01:46:00,179 WARNING Order book columns ['depth_imbalance5', 'ob_bid_vol_l1', 'ob_bid_vol_l2', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:00,180 INFO  📥 CoinAPI 1s message #87: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:00.0160000Z","time_coinapi":"2025-07-09T23:46:00.1805980Z","asks":[{"price":2.4077,"size":17998.2},{"price":2.4078,"size":17753.6},{"price":2.407...
2025-07-10 01:46:00,180 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:00.0160000Z', 'time_coinapi': '2025-07-09T23:46:00.1805980Z', 'asks': [{'price': 2.4077, 'size': 17998.2}, {'price': 2.4078, 'size': 17753.6},...
2025-07-10 01:46:00,180 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:00,183 INFO  Merging order book features: ['depth_imbalance5', 'ob_bid_vol_l1', 'ob_bid_vol_l2', 'depth_slope5']
2025-07-10 01:46:00,197 INFO  Successfully filled NaNs for merged OB columns: ['depth_imbalance5', 'ob_bid_vol_l1', 'ob_bid_vol_l2', 'depth_slope5']
2025-07-10 01:46:00,197 INFO  Calculating time-since-event features...
2025-07-10 01:46:00,300 INFO  📥 CoinAPI 1s message #88: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:00.1210000Z","time_coinapi":"2025-07-09T23:46:00.2862174Z","asks":[{"price":2.4784,"size":1979.5}],"bids":[{"price":2.4044,"size":14774.3},{"pric...
2025-07-10 01:46:00,306 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:00.1210000Z', 'time_coinapi': '2025-07-09T23:46:00.2862174Z', 'asks': [{'price': 2.4784, 'size': 1979.5}], 'bids': [{'price': 2.4044, 'size': ...
2025-07-10 01:46:00,306 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:00,421 INFO  📥 CoinAPI 1s message #89: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:00.2500000Z","time_coinapi":"2025-07-09T23:46:00.4124932Z","asks":[],"bids":[{"price":2.4062,"size":8034.0},{"price":2.4059,"size":8497.1}],"symb...
2025-07-10 01:46:00,439 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:00.2500000Z', 'time_coinapi': '2025-07-09T23:46:00.4124932Z', 'asks': [], 'bids': [{'price': 2.4062, 'size': 8034.0}, {'price': 2.4059, 'size'...
2025-07-10 01:46:00,444 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:00,606 INFO  📥 CoinAPI 1s message #90: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:00.4050000Z","time_coinapi":"2025-07-09T23:46:00.5750930Z","asks":[],"bids":[{"price":2.4062,"size":8059.8},{"price":2.4059,"size":8914.6},{"pric...
2025-07-10 01:46:00,612 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:00.4050000Z', 'time_coinapi': '2025-07-09T23:46:00.5750930Z', 'asks': [], 'bids': [{'price': 2.4062, 'size': 8059.8}, {'price': 2.4059, 'size'...
2025-07-10 01:46:00,618 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:00,763 INFO  📥 CoinAPI 1s message #91: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:00.5540000Z","time_coinapi":"2025-07-09T23:46:00.7309603Z","asks":[],"bids":[{"price":2.4062,"size":7561.2},{"price":2.4056,"size":11417.5},{"pri...
2025-07-10 01:46:00,769 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:00.5540000Z', 'time_coinapi': '2025-07-09T23:46:00.7309603Z', 'asks': [], 'bids': [{'price': 2.4062, 'size': 7561.2}, {'price': 2.4056, 'size'...
2025-07-10 01:46:00,775 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:00,818 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:00,819 INFO  Performing final cleanup...
2025-07-10 01:46:00,863 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:00,866 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:00,866 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:00,866 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:00,866 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:00,890 INFO  📥 CoinAPI 1s message #92: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:00.7150000Z","time_coinapi":"2025-07-09T23:46:00.8814929Z","asks":[{"price":2.4063,"size":7726.3}],"bids":[{"price":2.4030,"size":59777.4},{"pric...
2025-07-10 01:46:00,890 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:00.7150000Z', 'time_coinapi': '2025-07-09T23:46:00.8814929Z', 'asks': [{'price': 2.4063, 'size': 7726.3}], 'bids': [{'price': 2.403, 'size': 5...
2025-07-10 01:46:00,890 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:00,934 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:00,937 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:00,942 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'depth_imbalance5': 56523, 'depth_slope5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:00,959 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:00,969 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:00,969 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:00,970 INFO     Total features: 69
2025-07-10 01:46:00,970 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:00,970 INFO     DataFrame columns: 69
2025-07-10 01:46:00,999 INFO     close: 2.405400
2025-07-10 01:46:01,000 INFO     volume: 11.942529
2025-07-10 01:46:01,000 INFO     RSI_14: 35.795143
2025-07-10 01:46:01,000 INFO     EMA_21: 2.270887
2025-07-10 01:46:01,000 INFO     ATR_14: 0.000179
2025-07-10 01:46:01,000 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:01,000 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:01,000 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:01,001 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:01,001 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:01,001 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:01,055 INFO  📥 CoinAPI 1s message #93: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:00.8380000Z","time_coinapi":"2025-07-09T23:46:01.0016955Z","asks":[{"price":2.4063,"size":9218.8}],"bids":[{"price":2.4062,"size":8059.8}],"symbo...
2025-07-10 01:46:01,056 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:00.8380000Z', 'time_coinapi': '2025-07-09T23:46:01.0016955Z', 'asks': [{'price': 2.4063, 'size': 9218.8}], 'bids': [{'price': 2.4062, 'size': ...
2025-07-10 01:46:01,056 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:01,057 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:01,058 INFO  🔧 State vector dimensions:
2025-07-10 01:46:01,058 INFO     Expected total obs: 1451
2025-07-10 01:46:01,058 INFO     Lookback: 30
2025-07-10 01:46:01,058 INFO     Features needed: 48
2025-07-10 01:46:01,058 INFO     Features from config: 69
2025-07-10 01:46:01,058 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:01,058 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:01,058 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:01,058 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:01,058 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:01,058 WARNING    Truncating 21 features to match model
2025-07-10 01:46:01,058 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:01,061 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:01,062 INFO  🤖 Model decision: action=[-0.14881504 -0.83968216 -0.66219366  0.5522214 ], price=$2.4063, pos=0
2025-07-10 01:46:01,063 INFO  🔍 Action variance (last 10): [0.2808502  0.41298604 0.12953466 0.20387296]
2025-07-10 01:46:01,063 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:01,063 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406300
2025-07-10 01:46:01,063 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:01,063 INFO  🔍 State vector stats: mean=1.409904, std=1.875275, min=-0.258819, max=5.000000
2025-07-10 01:46:01,063 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:01,063 INFO  🔍 Normalized obs stats: mean=-2.228778, std=3.674745
2025-07-10 01:46:01,064 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4063]
2025-07-10 01:46:01,064 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:01,064 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:01,064 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:01,064 INFO  🔍 Feature variance - Close: 0.00000654, RSI: 0.00000000
2025-07-10 01:46:01,064 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:01,064 INFO  🔍 Current timestamp: 2025-07-09 23:46:01.001627+00:00
2025-07-10 01:46:01,064 INFO  🔍 1s mode: decision_ts=2025-07-09 23:45:53+00:00, current_ts=2025-07-09 23:46:01.001627+00:00
2025-07-10 01:46:01,064 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:45:53+00:00
2025-07-10 01:46:01,064 INFO  🎯 ENTRY CHECK: entry_sig=-0.054213, long_thr=0.7, short_thr=0.7, stable=True
2025-07-10 01:46:01,117 INFO  📥 CoinAPI 5m message #17: {"type":"hearbeat"}...
2025-07-10 01:46:01,117 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:01,199 INFO  📥 CoinAPI 1s message #94: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:01.0180000Z","time_coinapi":"2025-07-09T23:46:01.1811121Z","asks":[],"bids":[{"price":2.3341,"size":2099.4}],"symbol_id":"BINANCEFTS_PERP_XRP_USD...
2025-07-10 01:46:01,199 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:01.0180000Z', 'time_coinapi': '2025-07-09T23:46:01.1811121Z', 'asks': [], 'bids': [{'price': 2.3341, 'size': 2099.4}], 'symbol_id': 'BINANCEFT...
2025-07-10 01:46:01,199 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:01,200 INFO  🎯 EVALUATION: 1.1s since last, processing at 2025-07-09 23:46:01.200488+00:00
2025-07-10 01:46:01,200 INFO  📊 Calculating features for decision...
2025-07-10 01:46:01,200 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:01,207 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:01,207 INFO  Calculating technical indicators...
2025-07-10 01:46:01,258 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:01,259 INFO  Calculating and merging order flow features...
2025-07-10 01:46:01,261 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:01,266 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:01,273 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:01,274 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:01,275 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:01,275 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:01,285 INFO  Calculating and merging order book features...
2025-07-10 01:46:01,288 INFO  Calculated OB features: 22
2025-07-10 01:46:01,289 WARNING `_calc_ob_features` returned empty DataFrame or no columns.
2025-07-10 01:46:01,289 INFO  Calculating time-since-event features...
2025-07-10 01:46:01,383 INFO  📥 CoinAPI 1s message #95: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:01.1720000Z","time_coinapi":"2025-07-09T23:46:01.3400521Z","asks":[{"price":2.4063,"size":7726.3},{"price":2.4064,"size":7279.8},{"price":2.4073,...
2025-07-10 01:46:01,389 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:01.1720000Z', 'time_coinapi': '2025-07-09T23:46:01.3400521Z', 'asks': [{'price': 2.4063, 'size': 7726.3}, {'price': 2.4064, 'size': 7279.8}, {...
2025-07-10 01:46:01,397 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:01,478 INFO  📥 CoinAPI 1s message #96: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:01.2750000Z","time_coinapi":"2025-07-09T23:46:01.4422557Z","asks":[{"price":2.4063,"size":7719.4},{"price":2.4065,"size":4062.5},{"price":2.4066,...
2025-07-10 01:46:01,484 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:01.2750000Z', 'time_coinapi': '2025-07-09T23:46:01.4422557Z', 'asks': [{'price': 2.4063, 'size': 7719.4}, {'price': 2.4065, 'size': 4062.5}, {...
2025-07-10 01:46:01,499 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:01,576 INFO  📥 CoinAPI 1s message #97: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:01.3840000Z","time_coinapi":"2025-07-09T23:46:01.5477949Z","asks":[{"price":2.4063,"size":7726.3},{"price":2.4064,"size":7797.9},{"price":2.4065,...
2025-07-10 01:46:01,577 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:01.3840000Z', 'time_coinapi': '2025-07-09T23:46:01.5477949Z', 'asks': [{'price': 2.4063, 'size': 7726.3}, {'price': 2.4064, 'size': 7797.9}, {...
2025-07-10 01:46:01,577 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:01,691 INFO  📥 CoinAPI 1s message #98: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:01.4880000Z","time_coinapi":"2025-07-09T23:46:01.6503730Z","asks":[{"price":2.4064,"size":9290.4},{"price":2.4077,"size":17924.5}],"bids":[{"pric...
2025-07-10 01:46:01,697 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:01.4880000Z', 'time_coinapi': '2025-07-09T23:46:01.6503730Z', 'asks': [{'price': 2.4064, 'size': 9290.4}, {'price': 2.4077, 'size': 17924.5}],...
2025-07-10 01:46:01,703 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:01,853 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:01,853 INFO  Performing final cleanup...
2025-07-10 01:46:01,899 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:01,901 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:01,901 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:01,901 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:01,901 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:01,960 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:01,964 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:01,970 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:01,986 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:01,996 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:01,996 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:01,996 INFO     Total features: 69
2025-07-10 01:46:01,996 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:01,996 INFO     DataFrame columns: 69
2025-07-10 01:46:02,013 INFO     close: 2.405400
2025-07-10 01:46:02,013 INFO     volume: 11.942529
2025-07-10 01:46:02,013 INFO     RSI_14: 35.795143
2025-07-10 01:46:02,013 INFO     EMA_21: 2.270887
2025-07-10 01:46:02,013 INFO     ATR_14: 0.000179
2025-07-10 01:46:02,013 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:02,013 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:02,013 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:02,014 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:02,014 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:02,014 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:02,067 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:02,067 INFO  🔧 State vector dimensions:
2025-07-10 01:46:02,067 INFO     Expected total obs: 1451
2025-07-10 01:46:02,067 INFO     Lookback: 30
2025-07-10 01:46:02,067 INFO     Features needed: 48
2025-07-10 01:46:02,068 INFO     Features from config: 69
2025-07-10 01:46:02,068 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:02,068 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:02,068 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:02,068 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:02,068 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:02,068 WARNING    Truncating 21 features to match model
2025-07-10 01:46:02,068 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:02,070 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:02,072 INFO  🤖 Model decision: action=[ 0.61500955  0.4286909  -0.3260783   0.05056643], price=$2.4063, pos=0
2025-07-10 01:46:02,072 INFO  🔍 Action variance (last 10): [0.31183738 0.39707062 0.11188705 0.17490347]
2025-07-10 01:46:02,072 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:02,072 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406300
2025-07-10 01:46:02,072 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:02,072 INFO  🔍 State vector stats: mean=1.409904, std=1.875275, min=-0.258819, max=5.000000
2025-07-10 01:46:02,072 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:02,072 INFO  🔍 Normalized obs stats: mean=-2.228778, std=3.674745
2025-07-10 01:46:02,073 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4063]
2025-07-10 01:46:02,073 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:02,073 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:02,073 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:02,073 INFO  🔍 Feature variance - Close: 0.00000654, RSI: 0.00000000
2025-07-10 01:46:02,073 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:02,074 INFO  🔍 Current timestamp: 2025-07-09 23:46:02.014677+00:00
2025-07-10 01:46:02,074 INFO  🔍 1s mode: decision_ts=2025-07-09 23:45:53+00:00, current_ts=2025-07-09 23:46:02.014677+00:00
2025-07-10 01:46:02,074 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:45:53+00:00
2025-07-10 01:46:02,111 INFO  📥 CoinAPI 5m message #18: {"type":"hearbeat"}...
2025-07-10 01:46:02,111 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:02,690 INFO  📥 CoinAPI 1s message #99: {"type":"hearbeat"}...
2025-07-10 01:46:02,691 INFO  📊 CoinAPI 1s parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:02,840 INFO  📥 CoinAPI 1s message #100: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:02.4190000Z","time_coinapi":"2025-07-09T23:46:02.5944528Z","asks":[{"price":2.4077,"size":17877.5},{"price":2.4079,"size":8134.1},{"price":2.4090...
2025-07-10 01:46:02,841 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:02.4190000Z', 'time_coinapi': '2025-07-09T23:46:02.5944528Z', 'asks': [{'price': 2.4077, 'size': 17877.5}, {'price': 2.4079, 'size': 8134.1}, ...
2025-07-10 01:46:02,841 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:02,842 INFO  🎯 EVALUATION: 1.6s since last, processing at 2025-07-09 23:46:02.842613+00:00
2025-07-10 01:46:02,842 INFO  📊 Calculating features for decision...
2025-07-10 01:46:02,842 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:02,851 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:02,852 INFO  Calculating technical indicators...
2025-07-10 01:46:02,907 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:02,908 INFO  Calculating and merging order flow features...
2025-07-10 01:46:02,909 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:02,913 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:02,922 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:02,923 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:02,924 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:02,924 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:02,934 INFO  Calculating and merging order book features...
2025-07-10 01:46:02,937 INFO  Calculated OB features: 22
2025-07-10 01:46:02,938 WARNING Order book columns ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:02,941 INFO  Merging order book features: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'depth_slope5']
2025-07-10 01:46:02,962 INFO  Successfully filled NaNs for merged OB columns: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'depth_slope5']
2025-07-10 01:46:02,962 INFO  Calculating time-since-event features...
2025-07-10 01:46:03,137 INFO  📥 CoinAPI 5m message #19: {"type":"hearbeat"}...
2025-07-10 01:46:03,143 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:03,272 INFO  📥 CoinAPI 1s message #101: {"time_exchange":"2025-07-09T23:46:02.6110000Z","time_coinapi":"2025-07-09T23:46:02.7836282Z","uuid":"0188c35f-32ac-47a6-9cee-df2f9cfa2a28","price":2.4062,"size":207.7,"taker_side":"SELL","symbol_id":...
2025-07-10 01:46:03,278 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:02.6110000Z', 'time_coinapi': '2025-07-09T23:46:02.7836282Z', 'uuid': '0188c35f-32ac-47a6-9cee-df2f9cfa2a28', 'price': 2.4062, 'size': 207.7, 'taker_side': 'SELL', ...
2025-07-10 01:46:03,278 INFO  📊 CoinAPI trade received for 1s: price=2.4062, size=207.7
2025-07-10 01:46:03,284 INFO  📊 1s OHLCV from trades: $2.406300 vol=655.90
2025-07-10 01:46:03,294 INFO  📥 CoinAPI 1s message #102: {"time_exchange":"2025-07-09T23:46:02.6110000Z","time_coinapi":"2025-07-09T23:46:02.7836633Z","uuid":"c41c7cdc-bb03-4ff1-a196-1138372dbdf3","price":2.4062,"size":25.8,"taker_side":"SELL","symbol_id":"...
2025-07-10 01:46:03,300 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:02.6110000Z', 'time_coinapi': '2025-07-09T23:46:02.7836633Z', 'uuid': 'c41c7cdc-bb03-4ff1-a196-1138372dbdf3', 'price': 2.4062, 'size': 25.8, 'taker_side': 'SELL', '...
2025-07-10 01:46:03,312 INFO  📊 CoinAPI trade received for 1s: price=2.4062, size=25.8
2025-07-10 01:46:03,319 INFO  📥 CoinAPI 1s message #103: {"time_exchange":"2025-07-09T23:46:02.6110000Z","time_coinapi":"2025-07-09T23:46:02.7836721Z","uuid":"78c9de0e-d815-47c6-8696-bd4f5752b7f8","price":2.4062,"size":498.6,"taker_side":"SELL","symbol_id":...
2025-07-10 01:46:03,325 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:02.6110000Z', 'time_coinapi': '2025-07-09T23:46:02.7836721Z', 'uuid': '78c9de0e-d815-47c6-8696-bd4f5752b7f8', 'price': 2.4062, 'size': 498.6, 'taker_side': 'SELL', ...
2025-07-10 01:46:03,325 INFO  📊 CoinAPI trade received for 1s: price=2.4062, size=498.6
2025-07-10 01:46:03,325 INFO  📥 CoinAPI 1s message #104: {"time_exchange":"2025-07-09T23:46:02.6110000Z","time_coinapi":"2025-07-09T23:46:02.7836798Z","uuid":"f503df28-31ea-4475-aa3f-3e6cde3d79e0","price":2.4062,"size":36.8,"taker_side":"SELL","symbol_id":"...
2025-07-10 01:46:03,325 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:02.6110000Z', 'time_coinapi': '2025-07-09T23:46:02.7836798Z', 'uuid': 'f503df28-31ea-4475-aa3f-3e6cde3d79e0', 'price': 2.4062, 'size': 36.8, 'taker_side': 'SELL', '...
2025-07-10 01:46:03,325 INFO  📊 CoinAPI trade received for 1s: price=2.4062, size=36.8
2025-07-10 01:46:03,326 INFO  📥 CoinAPI 1s message #105: {"time_exchange":"2025-07-09T23:46:02.6110000Z","time_coinapi":"2025-07-09T23:46:02.7836868Z","uuid":"c8e35a53-900d-4eef-9253-ecfe7f6eb60c","price":2.4062,"size":16.9,"taker_side":"SELL","symbol_id":"...
2025-07-10 01:46:03,326 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:02.6110000Z', 'time_coinapi': '2025-07-09T23:46:02.7836868Z', 'uuid': 'c8e35a53-900d-4eef-9253-ecfe7f6eb60c', 'price': 2.4062, 'size': 16.9, 'taker_side': 'SELL', '...
2025-07-10 01:46:03,326 INFO  📊 CoinAPI trade received for 1s: price=2.4062, size=16.9
2025-07-10 01:46:03,326 INFO  📥 CoinAPI 1s message #106: {"time_exchange":"2025-07-09T23:46:02.6110000Z","time_coinapi":"2025-07-09T23:46:02.7836937Z","uuid":"9b062adf-b66b-4ec8-98a7-619c605c3a7d","price":2.4062,"size":45.7,"taker_side":"SELL","symbol_id":"...
2025-07-10 01:46:03,326 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:02.6110000Z', 'time_coinapi': '2025-07-09T23:46:02.7836937Z', 'uuid': '9b062adf-b66b-4ec8-98a7-619c605c3a7d', 'price': 2.4062, 'size': 45.7, 'taker_side': 'SELL', '...
2025-07-10 01:46:03,331 INFO  📊 CoinAPI trade received for 1s: price=2.4062, size=45.7
2025-07-10 01:46:03,331 INFO  📥 CoinAPI 1s message #107: {"time_exchange":"2025-07-09T23:46:02.6110000Z","time_coinapi":"2025-07-09T23:46:02.7837020Z","uuid":"b0630cda-9493-4662-80b0-9f114b769af6","price":2.4062,"size":45.6,"taker_side":"SELL","symbol_id":"...
2025-07-10 01:46:03,331 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:02.6110000Z', 'time_coinapi': '2025-07-09T23:46:02.7837020Z', 'uuid': 'b0630cda-9493-4662-80b0-9f114b769af6', 'price': 2.4062, 'size': 45.6, 'taker_side': 'SELL', '...
2025-07-10 01:46:03,331 INFO  📊 CoinAPI trade received for 1s: price=2.4062, size=45.6
2025-07-10 01:46:03,331 INFO  📥 CoinAPI 1s message #108: {"time_exchange":"2025-07-09T23:46:02.6110000Z","time_coinapi":"2025-07-09T23:46:02.7837115Z","uuid":"6e7138c7-d930-44da-ad91-3d520f90d2e3","price":2.4062,"size":6.9,"taker_side":"SELL","symbol_id":"B...
2025-07-10 01:46:03,331 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:02.6110000Z', 'time_coinapi': '2025-07-09T23:46:02.7837115Z', 'uuid': '6e7138c7-d930-44da-ad91-3d520f90d2e3', 'price': 2.4062, 'size': 6.9, 'taker_side': 'SELL', 's...
2025-07-10 01:46:03,331 INFO  📊 CoinAPI trade received for 1s: price=2.4062, size=6.9
2025-07-10 01:46:03,351 INFO  📥 CoinAPI 1s message #109: {"time_exchange":"2025-07-09T23:46:02.6860000Z","time_coinapi":"2025-07-09T23:46:02.8501340Z","uuid":"8f11c3ba-e297-47d9-944d-b0ce22180010","price":2.4059,"size":676.0,"taker_side":"SELL","symbol_id":...
2025-07-10 01:46:03,357 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:02.6860000Z', 'time_coinapi': '2025-07-09T23:46:02.8501340Z', 'uuid': '8f11c3ba-e297-47d9-944d-b0ce22180010', 'price': 2.4059, 'size': 676.0, 'taker_side': 'SELL', ...
2025-07-10 01:46:03,364 INFO  📊 CoinAPI trade received for 1s: price=2.4059, size=676.0
2025-07-10 01:46:03,370 INFO  📥 CoinAPI 1s message #110: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:03.0750000Z","time_coinapi":"2025-07-09T23:46:03.2382344Z","asks":[{"price":2.4057,"size":21381.8},{"price":2.4058,"size":14561.8},{"price":2.405...
2025-07-10 01:46:03,376 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:03.0750000Z', 'time_coinapi': '2025-07-09T23:46:03.2382344Z', 'asks': [{'price': 2.4057, 'size': 21381.8}, {'price': 2.4058, 'size': 14561.8},...
2025-07-10 01:46:03,383 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:03,410 INFO  📥 CoinAPI 1s message #111: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:03.1790000Z","time_coinapi":"2025-07-09T23:46:03.3418956Z","asks":[{"price":2.4057,"size":19889.3},{"price":2.4069,"size":8926.0},{"price":2.4070...
2025-07-10 01:46:03,416 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:03.1790000Z', 'time_coinapi': '2025-07-09T23:46:03.3418956Z', 'asks': [{'price': 2.4057, 'size': 19889.3}, {'price': 2.4069, 'size': 8926.0}, ...
2025-07-10 01:46:03,422 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:03,429 INFO  📥 CoinAPI 1s message #112: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:03.1790000Z","time_coinapi":"2025-07-09T23:46:03.3418956Z","asks":[{"price":2.4074,"size":11088.0},{"price":2.4075,"size":24073.6},{"price":2.407...
2025-07-10 01:46:03,435 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:03.1790000Z', 'time_coinapi': '2025-07-09T23:46:03.3418956Z', 'asks': [{'price': 2.4074, 'size': 11088.0}, {'price': 2.4075, 'size': 24073.6},...
2025-07-10 01:46:03,435 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:03,467 INFO  📥 CoinAPI 1s message #113: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:03.2890000Z","time_coinapi":"2025-07-09T23:46:03.4524181Z","asks":[{"price":2.4057,"size":21381.8},{"price":2.4071,"size":21691.5},{"price":2.407...
2025-07-10 01:46:03,472 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:03.2890000Z', 'time_coinapi': '2025-07-09T23:46:03.4524181Z', 'asks': [{'price': 2.4057, 'size': 21381.8}, {'price': 2.4071, 'size': 21691.5},...
2025-07-10 01:46:03,478 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:03,485 INFO  📥 CoinAPI 1s message #114: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:03.2890000Z","time_coinapi":"2025-07-09T23:46:03.4524181Z","asks":[{"price":2.4076,"size":13167.0}],"bids":[],"symbol_id":"BINANCEFTS_PERP_XRP_US...
2025-07-10 01:46:03,491 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:03.2890000Z', 'time_coinapi': '2025-07-09T23:46:03.4524181Z', 'asks': [{'price': 2.4076, 'size': 13167.0}], 'bids': [], 'symbol_id': 'BINANCEF...
2025-07-10 01:46:03,491 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:03,535 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:03,535 INFO  Performing final cleanup...
2025-07-10 01:46:03,551 INFO  📥 CoinAPI 1s message #115: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:03.3920000Z","time_coinapi":"2025-07-09T23:46:03.5550428Z","asks":[],"bids":[{"price":2.4052,"size":5011.4},{"price":2.4050,"size":7909.6},{"pric...
2025-07-10 01:46:03,551 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:03.3920000Z', 'time_coinapi': '2025-07-09T23:46:03.5550428Z', 'asks': [], 'bids': [{'price': 2.4052, 'size': 5011.4}, {'price': 2.405, 'size':...
2025-07-10 01:46:03,551 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:03,580 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:03,582 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:03,582 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:03,582 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:03,582 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:03,640 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:03,643 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:03,648 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'spread': 56523, 'mid_price': 56523, 'tob_imbalance': 56523, 'depth_imbalance5': 56523, 'depth_slope5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:03,661 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:03,662 INFO  📥 CoinAPI 1s message #116: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:03.5010000Z","time_coinapi":"2025-07-09T23:46:03.6639746Z","asks":[{"price":2.4059,"size":13179.6},{"price":2.4070,"size":17615.2},{"price":2.407...
2025-07-10 01:46:03,662 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:03.5010000Z', 'time_coinapi': '2025-07-09T23:46:03.6639746Z', 'asks': [{'price': 2.4059, 'size': 13179.6}, {'price': 2.407, 'size': 17615.2}, ...
2025-07-10 01:46:03,662 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:03,673 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:03,673 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:03,673 INFO     Total features: 69
2025-07-10 01:46:03,673 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:03,673 INFO     DataFrame columns: 69
2025-07-10 01:46:03,690 INFO     close: 2.405400
2025-07-10 01:46:03,690 INFO     volume: 11.942529
2025-07-10 01:46:03,690 INFO     RSI_14: 35.795143
2025-07-10 01:46:03,690 INFO     EMA_21: 2.270887
2025-07-10 01:46:03,690 INFO     ATR_14: 0.000179
2025-07-10 01:46:03,690 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:03,690 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:03,690 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:03,692 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:03,692 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:03,692 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:03,743 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:03,744 INFO  🔧 State vector dimensions:
2025-07-10 01:46:03,744 INFO     Expected total obs: 1451
2025-07-10 01:46:03,744 INFO     Lookback: 30
2025-07-10 01:46:03,744 INFO     Features needed: 48
2025-07-10 01:46:03,744 INFO     Features from config: 69
2025-07-10 01:46:03,744 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:03,744 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:03,744 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:03,744 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:03,744 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:03,744 WARNING    Truncating 21 features to match model
2025-07-10 01:46:03,744 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:03,746 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:03,747 INFO  🤖 Model decision: action=[ 0.4368639 -0.8189857 -0.3022927 -0.4860493], price=$2.4063, pos=0
2025-07-10 01:46:03,747 INFO  🔍 Action variance (last 10): [0.29753962 0.40720958 0.09815251 0.1810554 ]
2025-07-10 01:46:03,748 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:03,748 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406300
2025-07-10 01:46:03,748 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:03,748 INFO  🔍 State vector stats: mean=1.362960, std=1.881555, min=-0.258819, max=5.000000
2025-07-10 01:46:03,748 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:03,748 INFO  🔍 Normalized obs stats: mean=-2.367447, std=3.742415
2025-07-10 01:46:03,748 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4063]
2025-07-10 01:46:03,748 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:03,748 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:03,748 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:03,749 INFO  🔍 Feature variance - Close: 0.00000654, RSI: 0.00000000
2025-07-10 01:46:03,749 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:03,749 INFO  🔍 Current timestamp: 2025-07-09 23:46:03.692753+00:00
2025-07-10 01:46:03,749 INFO  🔍 1s mode: decision_ts=2025-07-09 23:45:53+00:00, current_ts=2025-07-09 23:46:03.692753+00:00
2025-07-10 01:46:03,749 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:45:53+00:00
2025-07-10 01:46:03,803 INFO  📥 CoinAPI 1s message #117: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:03.6280000Z","time_coinapi":"2025-07-09T23:46:03.7909194Z","asks":[{"price":2.4057,"size":18892.4},{"price":2.4074,"size":27667.5},{"price":2.407...
2025-07-10 01:46:03,803 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:03.6280000Z', 'time_coinapi': '2025-07-09T23:46:03.7909194Z', 'asks': [{'price': 2.4057, 'size': 18892.4}, {'price': 2.4074, 'size': 27667.5},...
2025-07-10 01:46:03,803 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:03,891 INFO  📥 CoinAPI 1s message #118: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:03.7340000Z","time_coinapi":"2025-07-09T23:46:03.8967539Z","asks":[{"price":2.4057,"size":18474.9},{"price":2.4068,"size":13295.1},{"price":2.407...
2025-07-10 01:46:03,891 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:03.7340000Z', 'time_coinapi': '2025-07-09T23:46:03.8967539Z', 'asks': [{'price': 2.4057, 'size': 18474.9}, {'price': 2.4068, 'size': 13295.1},...
2025-07-10 01:46:03,891 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:03,892 INFO  🎯 EVALUATION: 1.1s since last, processing at 2025-07-09 23:46:03.892652+00:00
2025-07-10 01:46:03,892 INFO  📊 Calculating features for decision...
2025-07-10 01:46:03,892 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:03,893 WARNING 🔍 DUPLICATE TIMESTAMPS DETECTED in timeframe 'trades': 7 duplicates
2025-07-10 01:46:03,893 WARNING    Index before dedup: 14 rows
2025-07-10 01:46:03,893 WARNING    Index after dedup: 7 rows
2025-07-10 01:46:03,898 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:03,898 INFO  Calculating technical indicators...
2025-07-10 01:46:03,951 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:03,952 INFO  Calculating and merging order flow features...
2025-07-10 01:46:03,954 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:03,957 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:03,965 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:03,967 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:03,968 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:03,968 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:03,979 INFO  Calculating and merging order book features...
2025-07-10 01:46:03,982 INFO  Calculated OB features: 22
2025-07-10 01:46:03,983 WARNING Order book columns ['depth_imbalance5', 'ob_ask_vol_l1', 'ob_ask_vol_l2', 'ob_ask_vol_l3', 'ob_ask_vol_l4', 'ob_ask_vol_l5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:03,986 INFO  Merging order book features: ['depth_imbalance5', 'ob_ask_vol_l1', 'ob_ask_vol_l2', 'ob_ask_vol_l3', 'ob_ask_vol_l4', 'ob_ask_vol_l5']
2025-07-10 01:46:04,000 INFO  Successfully filled NaNs for merged OB columns: ['depth_imbalance5', 'ob_ask_vol_l1', 'ob_ask_vol_l2', 'ob_ask_vol_l3', 'ob_ask_vol_l4', 'ob_ask_vol_l5']
2025-07-10 01:46:04,000 INFO  Calculating time-since-event features...
2025-07-10 01:46:04,049 INFO  📥 CoinAPI 1s message #119: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:03.8690000Z","time_coinapi":"2025-07-09T23:46:04.0330562Z","asks":[{"price":2.4058,"size":14979.3},{"price":2.4067,"size":14462.9},{"price":2.409...
2025-07-10 01:46:04,065 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:03.8690000Z', 'time_coinapi': '2025-07-09T23:46:04.0330562Z', 'asks': [{'price': 2.4058, 'size': 14979.3}, {'price': 2.4067, 'size': 14462.9},...
2025-07-10 01:46:04,078 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:04,144 INFO  📥 CoinAPI 5m message #20: {"type":"hearbeat"}...
2025-07-10 01:46:04,156 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:04,191 INFO  📥 CoinAPI 1s message #120: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:03.9990000Z","time_coinapi":"2025-07-09T23:46:04.1621239Z","asks":[{"price":2.4074,"size":26280.3},{"price":2.4075,"size":17381.3},{"price":2.409...
2025-07-10 01:46:04,207 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:03.9990000Z', 'time_coinapi': '2025-07-09T23:46:04.1621239Z', 'asks': [{'price': 2.4074, 'size': 26280.3}, {'price': 2.4075, 'size': 17381.3},...
2025-07-10 01:46:04,213 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:04,358 INFO  📥 CoinAPI 1s message #121: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:04.1750000Z","time_coinapi":"2025-07-09T23:46:04.3374480Z","asks":[{"price":2.4074,"size":21088.0},{"price":2.4075,"size":22573.6}],"bids":[{"pri...
2025-07-10 01:46:04,365 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:04.1750000Z', 'time_coinapi': '2025-07-09T23:46:04.3374480Z', 'asks': [{'price': 2.4074, 'size': 21088.0}, {'price': 2.4075, 'size': 22573.6}]...
2025-07-10 01:46:04,370 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:04,474 INFO  📥 CoinAPI 1s message #122: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:04.2790000Z","time_coinapi":"2025-07-09T23:46:04.4426573Z","asks":[{"price":2.4069,"size":7938.9},{"price":2.4070,"size":10215.2},{"price":2.4071...
2025-07-10 01:46:04,491 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:04.2790000Z', 'time_coinapi': '2025-07-09T23:46:04.4426573Z', 'asks': [{'price': 2.4069, 'size': 7938.9}, {'price': 2.407, 'size': 10215.2}, {...
2025-07-10 01:46:04,496 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:04,508 INFO  📥 CoinAPI 1s message #123: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:04.2790000Z","time_coinapi":"2025-07-09T23:46:04.4426573Z","asks":[{"price":2.4074,"size":26280.3},{"price":2.4075,"size":17381.3}],"bids":[],"sy...
2025-07-10 01:46:04,508 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:04.2790000Z', 'time_coinapi': '2025-07-09T23:46:04.4426573Z', 'asks': [{'price': 2.4074, 'size': 26280.3}, {'price': 2.4075, 'size': 17381.3}]...
2025-07-10 01:46:04,514 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:04,561 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:04,561 INFO  📥 CoinAPI 1s message #124: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:04.3840000Z","time_coinapi":"2025-07-09T23:46:04.5459608Z","asks":[{"price":2.4070,"size":17615.2},{"price":2.4072,"size":21672.0}],"bids":[{"pri...
2025-07-10 01:46:04,561 INFO  Performing final cleanup...
2025-07-10 01:46:04,562 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:04.3840000Z', 'time_coinapi': '2025-07-09T23:46:04.5459608Z', 'asks': [{'price': 2.407, 'size': 17615.2}, {'price': 2.4072, 'size': 21672.0}],...
2025-07-10 01:46:04,562 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:04,604 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:04,606 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:04,606 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:04,606 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:04,606 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:04,646 INFO  📥 CoinAPI 1s message #125: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:04.4860000Z","time_coinapi":"2025-07-09T23:46:04.6494568Z","asks":[{"price":2.4069,"size":8944.3},{"price":2.4090,"size":27841.5},{"price":2.4091...
2025-07-10 01:46:04,646 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:04.4860000Z', 'time_coinapi': '2025-07-09T23:46:04.6494568Z', 'asks': [{'price': 2.4069, 'size': 8944.3}, {'price': 2.409, 'size': 27841.5}, {...
2025-07-10 01:46:04,646 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:04,684 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:04,687 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:04,692 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'depth_imbalance5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:04,710 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:04,721 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:04,721 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:04,721 INFO     Total features: 69
2025-07-10 01:46:04,721 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:04,721 INFO     DataFrame columns: 69
2025-07-10 01:46:04,738 INFO     close: 2.405400
2025-07-10 01:46:04,738 INFO     volume: 11.942529
2025-07-10 01:46:04,738 INFO     RSI_14: 35.795143
2025-07-10 01:46:04,738 INFO     EMA_21: 2.270887
2025-07-10 01:46:04,738 INFO     ATR_14: 0.000179
2025-07-10 01:46:04,739 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:04,739 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:04,739 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:04,740 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:04,740 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:04,740 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:04,793 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:04,793 INFO  🔧 State vector dimensions:
2025-07-10 01:46:04,794 INFO     Expected total obs: 1451
2025-07-10 01:46:04,794 INFO     Lookback: 30
2025-07-10 01:46:04,794 INFO     Features needed: 48
2025-07-10 01:46:04,794 INFO     Features from config: 69
2025-07-10 01:46:04,794 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:04,794 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:04,794 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:04,794 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:04,794 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:04,794 WARNING    Truncating 21 features to match model
2025-07-10 01:46:04,794 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:04,795 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:04,797 INFO  🤖 Model decision: action=[-0.53185284 -0.25547326  0.17227876 -0.06819338], price=$2.4063, pos=0
2025-07-10 01:46:04,797 INFO  🔍 Action variance (last 10): [0.29468924 0.36264992 0.1057322  0.16099976]
2025-07-10 01:46:04,797 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:04,797 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406300
2025-07-10 01:46:04,797 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:04,797 INFO  🔍 State vector stats: mean=1.409904, std=1.875275, min=-0.258819, max=5.000000
2025-07-10 01:46:04,797 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:04,797 INFO  🔍 Normalized obs stats: mean=-2.228778, std=3.674745
2025-07-10 01:46:04,798 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4063]
2025-07-10 01:46:04,798 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:04,798 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:04,798 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:04,798 INFO  🔍 Feature variance - Close: 0.00000654, RSI: 0.00000000
2025-07-10 01:46:04,798 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:04,798 INFO  🔍 Current timestamp: 2025-07-09 23:46:04.740525+00:00
2025-07-10 01:46:04,798 INFO  🔍 1s mode: decision_ts=2025-07-09 23:45:54+00:00, current_ts=2025-07-09 23:46:04.740525+00:00
2025-07-10 01:46:04,798 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:45:54+00:00
2025-07-10 01:46:04,885 INFO  📥 CoinAPI 1s message #126: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:04.7250000Z","time_coinapi":"2025-07-09T23:46:04.8880936Z","asks":[{"price":2.4146,"size":2176.8}],"bids":[],"symbol_id":"BINANCEFTS_PERP_XRP_USD...
2025-07-10 01:46:04,885 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:04.7250000Z', 'time_coinapi': '2025-07-09T23:46:04.8880936Z', 'asks': [{'price': 2.4146, 'size': 2176.8}], 'bids': [], 'symbol_id': 'BINANCEFT...
2025-07-10 01:46:04,885 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:05,079 INFO  📥 CoinAPI 1s message #127: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:04.9170000Z","time_coinapi":"2025-07-09T23:46:05.0797292Z","asks":[{"price":2.4069,"size":7938.9},{"price":2.4070,"size":10215.2},{"price":2.4072...
2025-07-10 01:46:05,080 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:04.9170000Z', 'time_coinapi': '2025-07-09T23:46:05.0797292Z', 'asks': [{'price': 2.4069, 'size': 7938.9}, {'price': 2.407, 'size': 10215.2}, {...
2025-07-10 01:46:05,080 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:05,084 INFO  🎯 EVALUATION: 1.2s since last, processing at 2025-07-09 23:46:05.084487+00:00
2025-07-10 01:46:05,084 INFO  📊 Calculating features for decision...
2025-07-10 01:46:05,084 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:05,095 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:05,096 INFO  Calculating technical indicators...
2025-07-10 01:46:05,112 INFO  📥 CoinAPI 5m message #21: {"type":"hearbeat"}...
2025-07-10 01:46:05,112 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:05,160 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:05,160 INFO  Calculating and merging order flow features...
2025-07-10 01:46:05,162 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:05,165 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:05,173 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:05,174 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:05,174 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:05,174 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:05,183 INFO  📥 CoinAPI 1s message #128: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:05.0240000Z","time_coinapi":"2025-07-09T23:46:05.1876791Z","asks":[{"price":2.4070,"size":11460.3},{"price":2.4071,"size":27835.1},{"price":2.407...
2025-07-10 01:46:05,183 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:05.0240000Z', 'time_coinapi': '2025-07-09T23:46:05.1876791Z', 'asks': [{'price': 2.407, 'size': 11460.3}, {'price': 2.4071, 'size': 27835.1}, ...
2025-07-10 01:46:05,183 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:05,185 INFO  Calculating and merging order book features...
2025-07-10 01:46:05,189 INFO  Calculated OB features: 22
2025-07-10 01:46:05,190 WARNING Order book columns ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_ask_vol_l4', 'ob_ask_vol_l5', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:05,193 INFO  Merging order book features: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_ask_vol_l4', 'ob_ask_vol_l5', 'depth_slope5']
2025-07-10 01:46:05,210 INFO  Successfully filled NaNs for merged OB columns: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_ask_vol_l4', 'ob_ask_vol_l5', 'depth_slope5']
2025-07-10 01:46:05,210 INFO  Calculating time-since-event features...
2025-07-10 01:46:05,432 INFO  📥 CoinAPI 1s message #129: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:05.2400000Z","time_coinapi":"2025-07-09T23:46:05.4027796Z","asks":[{"price":2.4071,"size":27882.1},{"price":2.4073,"size":14075.7}],"bids":[{"pri...
2025-07-10 01:46:05,445 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:05.2400000Z', 'time_coinapi': '2025-07-09T23:46:05.4027796Z', 'asks': [{'price': 2.4071, 'size': 27882.1}, {'price': 2.4073, 'size': 14075.7}]...
2025-07-10 01:46:05,458 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:05,674 INFO  📥 CoinAPI 1s message #130: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:05.4700000Z","time_coinapi":"2025-07-09T23:46:05.6337543Z","asks":[{"price":2.4057,"size":0},{"price":2.4058,"size":3562.2},{"price":2.4059,"size...
2025-07-10 01:46:05,681 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:05.4700000Z', 'time_coinapi': '2025-07-09T23:46:05.6337543Z', 'asks': [{'price': 2.4057, 'size': 0}, {'price': 2.4058, 'size': 3562.2}, {'pric...
2025-07-10 01:46:05,687 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:05,766 INFO  📥 CoinAPI 1s message #131: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:05.4700000Z","time_coinapi":"2025-07-09T23:46:05.6337543Z","asks":[{"price":2.4077,"size":11473.9},{"price":2.4078,"size":11932.9},{"price":2.409...
2025-07-10 01:46:05,772 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:05.4700000Z', 'time_coinapi': '2025-07-09T23:46:05.6337543Z', 'asks': [{'price': 2.4077, 'size': 11473.9}, {'price': 2.4078, 'size': 11932.9},...
2025-07-10 01:46:05,778 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:05,778 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:05,778 INFO  Performing final cleanup...
2025-07-10 01:46:05,820 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:05,824 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:05,824 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:05,824 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:05,824 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:05,863 INFO  📥 CoinAPI 1s message #132: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:05.6790000Z","time_coinapi":"2025-07-09T23:46:05.8416401Z","asks":[{"price":2.4058,"size":2076.6},{"price":2.4059,"size":0},{"price":2.4060,"size...
2025-07-10 01:46:05,863 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:05.6790000Z', 'time_coinapi': '2025-07-09T23:46:05.8416401Z', 'asks': [{'price': 2.4058, 'size': 2076.6}, {'price': 2.4059, 'size': 0}, {'pric...
2025-07-10 01:46:05,863 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:05,883 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:05,885 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:05,887 INFO  📥 CoinAPI 1s message #133: {"time_exchange":"2025-07-09T23:46:05.6910000Z","time_coinapi":"2025-07-09T23:46:05.8544193Z","uuid":"96a2932e-85c8-43d6-a9c9-8c6c588d4704","price":2.4060,"size":2.3,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:46:05,888 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:05.6910000Z', 'time_coinapi': '2025-07-09T23:46:05.8544193Z', 'uuid': '96a2932e-85c8-43d6-a9c9-8c6c588d4704', 'price': 2.406, 'size': 2.3, 'taker_side': 'BUY', 'sym...
2025-07-10 01:46:05,888 INFO  📊 CoinAPI trade received for 1s: price=2.406, size=2.3
2025-07-10 01:46:05,888 INFO  📊 1s OHLCV from trades: $2.405900 vol=1560.00
2025-07-10 01:46:05,890 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'spread': 56523, 'mid_price': 56523, 'tob_imbalance': 56523, 'depth_imbalance5': 56523, 'depth_slope5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:05,891 INFO  📥 CoinAPI 1s message #134: {"time_exchange":"2025-07-09T23:46:05.6910000Z","time_coinapi":"2025-07-09T23:46:05.8544593Z","uuid":"880a3c48-d7e0-49c7-a81f-e4727b8e50fc","price":2.4060,"size":2.3,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:46:05,891 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:05.6910000Z', 'time_coinapi': '2025-07-09T23:46:05.8544593Z', 'uuid': '880a3c48-d7e0-49c7-a81f-e4727b8e50fc', 'price': 2.406, 'size': 2.3, 'taker_side': 'BUY', 'sym...
2025-07-10 01:46:05,891 INFO  📊 CoinAPI trade received for 1s: price=2.406, size=2.3
2025-07-10 01:46:05,891 INFO  📥 CoinAPI 1s message #135: {"time_exchange":"2025-07-09T23:46:05.6910000Z","time_coinapi":"2025-07-09T23:46:05.8544704Z","uuid":"acd47744-0d01-47ef-a76e-8213aa0ed480","price":2.4060,"size":2.3,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:46:05,891 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:05.6910000Z', 'time_coinapi': '2025-07-09T23:46:05.8544704Z', 'uuid': 'acd47744-0d01-47ef-a76e-8213aa0ed480', 'price': 2.406, 'size': 2.3, 'taker_side': 'BUY', 'sym...
2025-07-10 01:46:05,891 INFO  📊 CoinAPI trade received for 1s: price=2.406, size=2.3
2025-07-10 01:46:05,891 INFO  📥 CoinAPI 1s message #136: {"time_exchange":"2025-07-09T23:46:05.6910000Z","time_coinapi":"2025-07-09T23:46:05.8544839Z","uuid":"8b7cce9b-4dbb-4398-8b88-515827bbc75c","price":2.4060,"size":2.3,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:46:05,891 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:05.6910000Z', 'time_coinapi': '2025-07-09T23:46:05.8544839Z', 'uuid': '8b7cce9b-4dbb-4398-8b88-515827bbc75c', 'price': 2.406, 'size': 2.3, 'taker_side': 'BUY', 'sym...
2025-07-10 01:46:05,891 INFO  📊 CoinAPI trade received for 1s: price=2.406, size=2.3
2025-07-10 01:46:05,892 INFO  📥 CoinAPI 1s message #137: {"time_exchange":"2025-07-09T23:46:05.6910000Z","time_coinapi":"2025-07-09T23:46:05.8544956Z","uuid":"229abf4a-1d0a-49e1-8a37-41d07d14ffee","price":2.4060,"size":2.3,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:46:05,892 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:05.6910000Z', 'time_coinapi': '2025-07-09T23:46:05.8544956Z', 'uuid': '229abf4a-1d0a-49e1-8a37-41d07d14ffee', 'price': 2.406, 'size': 2.3, 'taker_side': 'BUY', 'sym...
2025-07-10 01:46:05,892 INFO  📊 CoinAPI trade received for 1s: price=2.406, size=2.3
2025-07-10 01:46:05,892 INFO  📥 CoinAPI 1s message #138: {"time_exchange":"2025-07-09T23:46:05.6910000Z","time_coinapi":"2025-07-09T23:46:05.8545058Z","uuid":"97c7c2de-ebc2-48a1-a649-34633ad5af4e","price":2.4060,"size":2.3,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:46:05,892 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:05.6910000Z', 'time_coinapi': '2025-07-09T23:46:05.8545058Z', 'uuid': '97c7c2de-ebc2-48a1-a649-34633ad5af4e', 'price': 2.406, 'size': 2.3, 'taker_side': 'BUY', 'sym...
2025-07-10 01:46:05,892 INFO  📊 CoinAPI trade received for 1s: price=2.406, size=2.3
2025-07-10 01:46:05,892 INFO  📥 CoinAPI 1s message #139: {"time_exchange":"2025-07-09T23:46:05.6910000Z","time_coinapi":"2025-07-09T23:46:05.8545131Z","uuid":"a11f0d39-d034-496e-9c45-5efb60b1536b","price":2.4060,"size":2.3,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:46:05,892 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:05.6910000Z', 'time_coinapi': '2025-07-09T23:46:05.8545131Z', 'uuid': 'a11f0d39-d034-496e-9c45-5efb60b1536b', 'price': 2.406, 'size': 2.3, 'taker_side': 'BUY', 'sym...
2025-07-10 01:46:05,892 INFO  📊 CoinAPI trade received for 1s: price=2.406, size=2.3
2025-07-10 01:46:05,892 INFO  📥 CoinAPI 1s message #140: {"time_exchange":"2025-07-09T23:46:05.6910000Z","time_coinapi":"2025-07-09T23:46:05.8545199Z","uuid":"68d08d99-b3ec-4892-a043-1c764848b96a","price":2.4060,"size":2.3,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:46:05,892 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:05.6910000Z', 'time_coinapi': '2025-07-09T23:46:05.8545199Z', 'uuid': '68d08d99-b3ec-4892-a043-1c764848b96a', 'price': 2.406, 'size': 2.3, 'taker_side': 'BUY', 'sym...
2025-07-10 01:46:05,892 INFO  📊 CoinAPI trade received for 1s: price=2.406, size=2.3
2025-07-10 01:46:05,892 INFO  📥 CoinAPI 1s message #141: {"time_exchange":"2025-07-09T23:46:05.6920000Z","time_coinapi":"2025-07-09T23:46:05.8545271Z","uuid":"46d740ce-71c9-4a8b-bfe0-503e1ae88a52","price":2.4060,"size":2.3,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:46:05,892 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:05.6920000Z', 'time_coinapi': '2025-07-09T23:46:05.8545271Z', 'uuid': '46d740ce-71c9-4a8b-bfe0-503e1ae88a52', 'price': 2.406, 'size': 2.3, 'taker_side': 'BUY', 'sym...
2025-07-10 01:46:05,892 INFO  📊 CoinAPI trade received for 1s: price=2.406, size=2.3
2025-07-10 01:46:05,892 INFO  📥 CoinAPI 1s message #142: {"time_exchange":"2025-07-09T23:46:05.6920000Z","time_coinapi":"2025-07-09T23:46:05.8545359Z","uuid":"b8c13b6b-64af-4298-9680-181b666fea90","price":2.4060,"size":2.3,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:46:05,892 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:05.6920000Z', 'time_coinapi': '2025-07-09T23:46:05.8545359Z', 'uuid': 'b8c13b6b-64af-4298-9680-181b666fea90', 'price': 2.406, 'size': 2.3, 'taker_side': 'BUY', 'sym...
2025-07-10 01:46:05,892 INFO  📊 CoinAPI trade received for 1s: price=2.406, size=2.3
2025-07-10 01:46:05,906 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:05,915 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:05,915 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:05,915 INFO     Total features: 69
2025-07-10 01:46:05,915 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:05,915 INFO     DataFrame columns: 69
2025-07-10 01:46:06,037 INFO     close: 2.405400
2025-07-10 01:46:06,037 INFO     volume: 11.942529
2025-07-10 01:46:06,037 INFO     RSI_14: 35.795143
2025-07-10 01:46:06,037 INFO     EMA_21: 2.270887
2025-07-10 01:46:06,037 INFO     ATR_14: 0.000179
2025-07-10 01:46:06,037 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:06,037 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:06,037 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:06,038 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:06,038 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:06,038 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:06,087 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:06,087 INFO  🔧 State vector dimensions:
2025-07-10 01:46:06,088 INFO     Expected total obs: 1451
2025-07-10 01:46:06,088 INFO     Lookback: 30
2025-07-10 01:46:06,088 INFO     Features needed: 48
2025-07-10 01:46:06,088 INFO     Features from config: 69
2025-07-10 01:46:06,088 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:06,088 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:06,088 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:06,088 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:06,088 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:06,088 WARNING    Truncating 21 features to match model
2025-07-10 01:46:06,088 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:06,090 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:06,091 INFO  🤖 Model decision: action=[ 0.5849774  -0.5059113   0.85642755 -0.6672699 ], price=$2.4063, pos=0
2025-07-10 01:46:06,091 INFO  🔍 Action variance (last 10): [0.30040178 0.33586267 0.19795093 0.17963894]
2025-07-10 01:46:06,091 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:06,091 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406300
2025-07-10 01:46:06,092 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:06,092 INFO  🔍 State vector stats: mean=1.362960, std=1.881555, min=-0.258819, max=5.000000
2025-07-10 01:46:06,092 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:06,092 INFO  🔍 Normalized obs stats: mean=-2.367447, std=3.742415
2025-07-10 01:46:06,092 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4063]
2025-07-10 01:46:06,093 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:06,093 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:06,093 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:06,093 INFO  🔍 Feature variance - Close: 0.00000654, RSI: 0.00000000
2025-07-10 01:46:06,093 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:06,093 INFO  🔍 Current timestamp: 2025-07-09 23:46:06.038915+00:00
2025-07-10 01:46:06,093 INFO  🔍 1s mode: decision_ts=2025-07-09 23:45:54+00:00, current_ts=2025-07-09 23:46:06.038915+00:00
2025-07-10 01:46:06,093 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:45:54+00:00
2025-07-10 01:46:06,093 INFO  🎯 ENTRY CHECK: entry_sig=0.178291, long_thr=0.7, short_thr=0.7, stable=True
2025-07-10 01:46:06,094 INFO  🎯 EVALUATION: 1.0s since last, processing at 2025-07-09 23:46:06.094334+00:00
2025-07-10 01:46:06,094 INFO  📊 Calculating features for decision...
2025-07-10 01:46:06,094 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:06,100 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:06,101 INFO  Calculating technical indicators...
2025-07-10 01:46:06,144 INFO  📥 CoinAPI 5m message #22: {"type":"hearbeat"}...
2025-07-10 01:46:06,144 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:06,149 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:06,149 INFO  Calculating and merging order flow features...
2025-07-10 01:46:06,151 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:06,155 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:06,163 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:06,165 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:06,165 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:06,166 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:06,176 INFO  Calculating and merging order book features...
2025-07-10 01:46:06,186 INFO  Calculated OB features: 22
2025-07-10 01:46:06,188 WARNING Order book columns ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:06,192 INFO  📥 CoinAPI 1s message #143: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:05.7800000Z","time_coinapi":"2025-07-09T23:46:05.9429539Z","asks":[{"price":2.4058,"size":0},{"price":2.4060,"size":0},{"price":2.4061,"size":663...
2025-07-10 01:46:06,192 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:05.7800000Z', 'time_coinapi': '2025-07-09T23:46:05.9429539Z', 'asks': [{'price': 2.4058, 'size': 0}, {'price': 2.406, 'size': 0}, {'price': 2....
2025-07-10 01:46:06,192 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:06,194 INFO  Merging order book features: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'depth_slope5']
2025-07-10 01:46:06,240 INFO  Successfully filled NaNs for merged OB columns: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'depth_slope5']
2025-07-10 01:46:06,240 INFO  Calculating time-since-event features...
2025-07-10 01:46:06,400 INFO  📥 CoinAPI 1s message #144: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:05.9920000Z","time_coinapi":"2025-07-09T23:46:06.1552282Z","asks":[{"price":2.4062,"size":10208.9},{"price":2.4063,"size":10129.5},{"price":2.406...
2025-07-10 01:46:06,407 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:05.9920000Z', 'time_coinapi': '2025-07-09T23:46:06.1552282Z', 'asks': [{'price': 2.4062, 'size': 10208.9}, {'price': 2.4063, 'size': 10129.5},...
2025-07-10 01:46:06,413 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:06,802 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:06,802 INFO  Performing final cleanup...
2025-07-10 01:46:06,843 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:06,845 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:06,845 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:06,845 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:06,845 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:06,904 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:06,906 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:06,911 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'spread': 56523, 'mid_price': 56523, 'tob_imbalance': 56523, 'depth_imbalance5': 56523, 'depth_slope5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:06,927 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:06,939 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:06,939 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:06,939 INFO     Total features: 69
2025-07-10 01:46:06,939 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:06,939 INFO     DataFrame columns: 69
2025-07-10 01:46:06,957 INFO     close: 2.405400
2025-07-10 01:46:06,957 INFO     volume: 11.942529
2025-07-10 01:46:06,958 INFO     RSI_14: 35.795143
2025-07-10 01:46:06,958 INFO     EMA_21: 2.270887
2025-07-10 01:46:06,958 INFO     ATR_14: 0.000179
2025-07-10 01:46:06,958 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:06,958 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:06,958 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:06,960 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:06,960 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:06,960 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:07,013 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:07,014 INFO  🔧 State vector dimensions:
2025-07-10 01:46:07,014 INFO     Expected total obs: 1451
2025-07-10 01:46:07,014 INFO     Lookback: 30
2025-07-10 01:46:07,014 INFO     Features needed: 48
2025-07-10 01:46:07,014 INFO     Features from config: 69
2025-07-10 01:46:07,014 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:07,014 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:07,014 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:07,014 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:07,014 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:07,014 WARNING    Truncating 21 features to match model
2025-07-10 01:46:07,014 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:07,016 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:07,018 INFO  🤖 Model decision: action=[ 0.17930925 -0.13766915 -0.47016323  0.2719028 ], price=$2.4063, pos=0
2025-07-10 01:46:07,018 INFO  🔍 Action variance (last 10): [0.2876598  0.31230175 0.20761624 0.19206293]
2025-07-10 01:46:07,018 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:07,018 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406300
2025-07-10 01:46:07,018 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:07,018 INFO  🔍 State vector stats: mean=1.362960, std=1.881555, min=-0.258819, max=5.000000
2025-07-10 01:46:07,018 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:07,018 INFO  🔍 Normalized obs stats: mean=-2.367447, std=3.742415
2025-07-10 01:46:07,019 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4063]
2025-07-10 01:46:07,019 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:07,019 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:07,019 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:07,019 INFO  🔍 Feature variance - Close: 0.00000654, RSI: 0.00000000
2025-07-10 01:46:07,019 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:07,019 INFO  🔍 Current timestamp: 2025-07-09 23:46:06.960518+00:00
2025-07-10 01:46:07,019 INFO  🔍 1s mode: decision_ts=2025-07-09 23:45:54+00:00, current_ts=2025-07-09 23:46:06.960518+00:00
2025-07-10 01:46:07,019 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:45:54+00:00
2025-07-10 01:46:07,114 INFO  📥 CoinAPI 5m message #23: {"type":"hearbeat"}...
2025-07-10 01:46:07,114 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:07,374 INFO  📥 CoinAPI 1s message #145: {"type":"hearbeat"}...
2025-07-10 01:46:07,374 INFO  📊 CoinAPI 1s parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:07,459 INFO  📥 CoinAPI 1s message #146: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:07.2990000Z","time_coinapi":"2025-07-09T23:46:07.4622801Z","asks":[{"price":2.4061,"size":14186.3},{"price":2.4062,"size":8716.4},{"price":2.4063...
2025-07-10 01:46:07,460 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:07.2990000Z', 'time_coinapi': '2025-07-09T23:46:07.4622801Z', 'asks': [{'price': 2.4061, 'size': 14186.3}, {'price': 2.4062, 'size': 8716.4}, ...
2025-07-10 01:46:07,460 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:07,461 INFO  🎯 EVALUATION: 1.4s since last, processing at 2025-07-09 23:46:07.461383+00:00
2025-07-10 01:46:07,461 INFO  📊 Calculating features for decision...
2025-07-10 01:46:07,461 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:07,462 WARNING 🔍 DUPLICATE TIMESTAMPS DETECTED in timeframe 'trades': 8 duplicates
2025-07-10 01:46:07,462 WARNING    Index before dedup: 17 rows
2025-07-10 01:46:07,462 WARNING    Index after dedup: 9 rows
2025-07-10 01:46:07,470 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:07,471 INFO  Calculating technical indicators...
2025-07-10 01:46:07,526 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:07,527 INFO  Calculating and merging order flow features...
2025-07-10 01:46:07,528 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:07,533 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:07,541 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:07,542 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:07,543 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:07,543 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:07,552 INFO  Calculating and merging order book features...
2025-07-10 01:46:07,556 INFO  Calculated OB features: 22
2025-07-10 01:46:07,557 WARNING Order book columns ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:07,560 INFO  Merging order book features: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'depth_slope5']
2025-07-10 01:46:07,583 INFO  Successfully filled NaNs for merged OB columns: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'depth_slope5']
2025-07-10 01:46:07,583 INFO  Calculating time-since-event features...
2025-07-10 01:46:07,622 INFO  📥 CoinAPI 1s message #147: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:07.4070000Z","time_coinapi":"2025-07-09T23:46:07.5767947Z","asks":[{"price":2.4069,"size":8721.8},{"price":2.4074,"size":24853.3},{"price":2.4076...
2025-07-10 01:46:07,634 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:07.4070000Z', 'time_coinapi': '2025-07-09T23:46:07.5767947Z', 'asks': [{'price': 2.4069, 'size': 8721.8}, {'price': 2.4074, 'size': 24853.3}, ...
2025-07-10 01:46:07,634 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:07,702 INFO  📥 CoinAPI 1s message #148: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:07.5130000Z","time_coinapi":"2025-07-09T23:46:07.6755426Z","asks":[{"price":2.4093,"size":3721.2},{"price":2.4399,"size":10551.2}],"bids":[],"sym...
2025-07-10 01:46:07,708 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:07.5130000Z', 'time_coinapi': '2025-07-09T23:46:07.6755426Z', 'asks': [{'price': 2.4093, 'size': 3721.2}, {'price': 2.4399, 'size': 10551.2}],...
2025-07-10 01:46:07,715 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:08,136 INFO  📥 CoinAPI 5m message #24: {"type":"hearbeat"}...
2025-07-10 01:46:08,142 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:08,143 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:08,143 INFO  Performing final cleanup...
2025-07-10 01:46:08,185 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:08,187 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:08,187 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:08,187 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:08,187 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:08,246 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:08,248 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:08,254 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'spread': 56523, 'mid_price': 56523, 'tob_imbalance': 56523, 'depth_imbalance5': 56523, 'depth_slope5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:08,270 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:08,282 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:08,282 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:08,282 INFO     Total features: 69
2025-07-10 01:46:08,283 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:08,283 INFO     DataFrame columns: 69
2025-07-10 01:46:08,302 INFO     close: 2.405400
2025-07-10 01:46:08,302 INFO     volume: 11.942529
2025-07-10 01:46:08,302 INFO     RSI_14: 35.795143
2025-07-10 01:46:08,302 INFO     EMA_21: 2.270887
2025-07-10 01:46:08,302 INFO     ATR_14: 0.000179
2025-07-10 01:46:08,303 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:08,303 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:08,303 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:08,304 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:08,304 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:08,304 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:08,354 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:08,355 INFO  🔧 State vector dimensions:
2025-07-10 01:46:08,355 INFO     Expected total obs: 1451
2025-07-10 01:46:08,355 INFO     Lookback: 30
2025-07-10 01:46:08,355 INFO     Features needed: 48
2025-07-10 01:46:08,355 INFO     Features from config: 69
2025-07-10 01:46:08,355 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:08,355 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:08,356 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:08,356 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:08,356 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:08,356 WARNING    Truncating 21 features to match model
2025-07-10 01:46:08,356 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:08,358 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:08,359 INFO  🤖 Model decision: action=[ 0.9061675  0.941499  -0.8132416 -0.7396533], price=$2.4059, pos=0
2025-07-10 01:46:08,359 INFO  🔍 Action variance (last 10): [0.34234452 0.30526668 0.24144554 0.23333998]
2025-07-10 01:46:08,359 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:08,359 INFO  🔍 Feature frame shape: (30, 48), last close: 2.405900
2025-07-10 01:46:08,359 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:08,360 INFO  🔍 State vector stats: mean=1.362960, std=1.881554, min=-0.258819, max=5.000000
2025-07-10 01:46:08,360 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:08,360 INFO  🔍 Normalized obs stats: mean=-2.367449, std=3.742414
2025-07-10 01:46:08,360 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4059]
2025-07-10 01:46:08,360 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:08,360 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:08,360 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:08,360 INFO  🔍 Feature variance - Close: 0.00000715, RSI: 0.00000000
2025-07-10 01:46:08,360 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:08,360 INFO  🔍 Current timestamp: 2025-07-09 23:46:08.304293+00:00
2025-07-10 01:46:08,361 INFO  🔍 1s mode: decision_ts=2025-07-09 23:46:02+00:00, current_ts=2025-07-09 23:46:08.304293+00:00
2025-07-10 01:46:08,361 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:46:02+00:00
2025-07-10 01:46:08,444 INFO  📥 CoinAPI 1s message #149: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:07.9770000Z","time_coinapi":"2025-07-09T23:46:08.1428471Z","asks":[{"price":2.4061,"size":13768.8},{"price":2.4074,"size":24806.3},{"price":2.407...
2025-07-10 01:46:08,444 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:07.9770000Z', 'time_coinapi': '2025-07-09T23:46:08.1428471Z', 'asks': [{'price': 2.4061, 'size': 13768.8}, {'price': 2.4074, 'size': 24806.3},...
2025-07-10 01:46:08,444 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:08,564 INFO  📥 CoinAPI 1s message #150: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:08.4000000Z","time_coinapi":"2025-07-09T23:46:08.5626209Z","asks":[{"price":2.4061,"size":10281.6},{"price":2.4062,"size":9133.9},{"price":2.4066...
2025-07-10 01:46:08,564 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:08.4000000Z', 'time_coinapi': '2025-07-09T23:46:08.5626209Z', 'asks': [{'price': 2.4061, 'size': 10281.6}, {'price': 2.4062, 'size': 9133.9}, ...
2025-07-10 01:46:08,564 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:08,565 INFO  🎯 EVALUATION: 1.1s since last, processing at 2025-07-09 23:46:08.565782+00:00
2025-07-10 01:46:08,565 INFO  📊 Calculating features for decision...
2025-07-10 01:46:08,565 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:08,566 INFO  📥 CoinAPI 1s message #151: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:08.4000000Z","time_coinapi":"2025-07-09T23:46:08.5626209Z","asks":[{"price":2.4077,"size":14315.0},{"price":2.4083,"size":40179.2},{"price":2.409...
2025-07-10 01:46:08,566 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:08.4000000Z', 'time_coinapi': '2025-07-09T23:46:08.5626209Z', 'asks': [{'price': 2.4077, 'size': 14315.0}, {'price': 2.4083, 'size': 40179.2},...
2025-07-10 01:46:08,566 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:08,571 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:08,571 INFO  Calculating technical indicators...
2025-07-10 01:46:08,623 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:08,623 INFO  Calculating and merging order flow features...
2025-07-10 01:46:08,625 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:08,628 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:08,637 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:08,638 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:08,638 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:08,639 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:08,650 INFO  Calculating and merging order book features...
2025-07-10 01:46:08,655 INFO  Calculated OB features: 22
2025-07-10 01:46:08,657 WARNING Order book columns ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:08,659 INFO  Merging order book features: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'depth_slope5']
2025-07-10 01:46:08,676 INFO  📥 CoinAPI 1s message #152: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:08.5040000Z","time_coinapi":"2025-07-09T23:46:08.6664144Z","asks":[{"price":2.4078,"size":13401.0},{"price":2.4079,"size":16943.1},{"price":2.409...
2025-07-10 01:46:08,676 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:08.5040000Z', 'time_coinapi': '2025-07-09T23:46:08.6664144Z', 'asks': [{'price': 2.4078, 'size': 13401.0}, {'price': 2.4079, 'size': 16943.1},...
2025-07-10 01:46:08,676 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:08,692 INFO  Successfully filled NaNs for merged OB columns: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'depth_slope5']
2025-07-10 01:46:08,692 INFO  Calculating time-since-event features...
2025-07-10 01:46:09,126 INFO  📥 CoinAPI 5m message #25: {"type":"hearbeat"}...
2025-07-10 01:46:09,139 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:09,232 INFO  📥 CoinAPI 1s message #153: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:09.0250000Z","time_coinapi":"2025-07-09T23:46:09.1876571Z","asks":[{"price":2.4066,"size":9201.1},{"price":2.4067,"size":13590.9},{"price":2.4077...
2025-07-10 01:46:09,238 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:09.0250000Z', 'time_coinapi': '2025-07-09T23:46:09.1876571Z', 'asks': [{'price': 2.4066, 'size': 9201.1}, {'price': 2.4067, 'size': 13590.9}, ...
2025-07-10 01:46:09,238 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:09,248 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:09,249 INFO  Performing final cleanup...
2025-07-10 01:46:09,291 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:09,294 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:09,294 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:09,294 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:09,294 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:09,332 INFO  📥 CoinAPI 1s message #154: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:09.1600000Z","time_coinapi":"2025-07-09T23:46:09.3223389Z","asks":[],"bids":[{"price":2.4059,"size":1589.3},{"price":2.4058,"size":8392.5}],"symb...
2025-07-10 01:46:09,333 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:09.1600000Z', 'time_coinapi': '2025-07-09T23:46:09.3223389Z', 'asks': [], 'bids': [{'price': 2.4059, 'size': 1589.3}, {'price': 2.4058, 'size'...
2025-07-10 01:46:09,333 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:09,357 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:09,360 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:09,365 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'spread': 56523, 'mid_price': 56523, 'tob_imbalance': 56523, 'depth_imbalance5': 56523, 'depth_slope5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:09,380 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:09,392 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:09,392 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:09,392 INFO     Total features: 69
2025-07-10 01:46:09,392 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:09,392 INFO     DataFrame columns: 69
2025-07-10 01:46:09,410 INFO     close: 2.405400
2025-07-10 01:46:09,410 INFO     volume: 11.942529
2025-07-10 01:46:09,410 INFO     RSI_14: 35.795143
2025-07-10 01:46:09,410 INFO     EMA_21: 2.270887
2025-07-10 01:46:09,410 INFO     ATR_14: 0.000179
2025-07-10 01:46:09,410 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:09,410 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:09,410 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:09,411 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:09,411 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:09,411 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:09,461 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:09,461 INFO  🔧 State vector dimensions:
2025-07-10 01:46:09,461 INFO     Expected total obs: 1451
2025-07-10 01:46:09,461 INFO     Lookback: 30
2025-07-10 01:46:09,461 INFO     Features needed: 48
2025-07-10 01:46:09,461 INFO     Features from config: 69
2025-07-10 01:46:09,461 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:09,461 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:09,462 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:09,462 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:09,462 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:09,462 WARNING    Truncating 21 features to match model
2025-07-10 01:46:09,462 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:09,463 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:09,465 INFO  🤖 Model decision: action=[-0.930138   -0.81833553  0.72751236 -0.7068182 ], price=$2.4059, pos=0
2025-07-10 01:46:09,465 INFO  🔍 Action variance (last 10): [0.33835799 0.32605204 0.30172133 0.26281276]
2025-07-10 01:46:09,465 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:09,465 INFO  🔍 Feature frame shape: (30, 48), last close: 2.405900
2025-07-10 01:46:09,465 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:09,465 INFO  🔍 State vector stats: mean=1.362960, std=1.881554, min=-0.258819, max=5.000000
2025-07-10 01:46:09,465 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:09,465 INFO  🔍 Normalized obs stats: mean=-2.367449, std=3.742414
2025-07-10 01:46:09,466 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4059]
2025-07-10 01:46:09,466 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:09,466 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:09,466 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:09,467 INFO  🔍 Feature variance - Close: 0.00000715, RSI: 0.00000000
2025-07-10 01:46:09,467 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:09,467 INFO  🔍 Current timestamp: 2025-07-09 23:46:09.411588+00:00
2025-07-10 01:46:09,467 INFO  🔍 1s mode: decision_ts=2025-07-09 23:46:02+00:00, current_ts=2025-07-09 23:46:09.411588+00:00
2025-07-10 01:46:09,467 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:46:02+00:00
2025-07-10 01:46:09,500 INFO  📥 CoinAPI 1s message #155: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:09.3440000Z","time_coinapi":"2025-07-09T23:46:09.5064287Z","asks":[],"bids":[{"price":2.4060,"size":7879.9},{"price":2.4059,"size":3401.9}],"symb...
2025-07-10 01:46:09,500 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:09.3440000Z', 'time_coinapi': '2025-07-09T23:46:09.5064287Z', 'asks': [], 'bids': [{'price': 2.406, 'size': 7879.9}, {'price': 2.4059, 'size':...
2025-07-10 01:46:09,500 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:09,602 INFO  📥 CoinAPI 1s message #156: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:09.4470000Z","time_coinapi":"2025-07-09T23:46:09.6090984Z","asks":[{"price":2.4073,"size":10949.0},{"price":2.4095,"size":15472.2}],"bids":[{"pri...
2025-07-10 01:46:09,602 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:09.4470000Z', 'time_coinapi': '2025-07-09T23:46:09.6090984Z', 'asks': [{'price': 2.4073, 'size': 10949.0}, {'price': 2.4095, 'size': 15472.2}]...
2025-07-10 01:46:09,602 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:09,603 INFO  🎯 EVALUATION: 1.0s since last, processing at 2025-07-09 23:46:09.603582+00:00
2025-07-10 01:46:09,603 INFO  📊 Calculating features for decision...
2025-07-10 01:46:09,603 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:09,611 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:09,612 INFO  Calculating technical indicators...
2025-07-10 01:46:09,666 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:09,666 INFO  Calculating and merging order flow features...
2025-07-10 01:46:09,668 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:09,672 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:09,678 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:09,680 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:09,680 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:09,680 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:09,689 INFO  Calculating and merging order book features...
2025-07-10 01:46:09,693 INFO  Calculated OB features: 22
2025-07-10 01:46:09,694 WARNING Order book columns ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_bid_vol_l1', 'ob_ask_vol_l1'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:09,697 INFO  Merging order book features: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1']
2025-07-10 01:46:09,709 INFO  Successfully filled NaNs for merged OB columns: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1']
2025-07-10 01:46:09,709 INFO  Calculating time-since-event features...
2025-07-10 01:46:09,785 INFO  📥 CoinAPI 1s message #157: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:09.5740000Z","time_coinapi":"2025-07-09T23:46:09.7369917Z","asks":[],"bids":[{"price":2.4060,"size":7879.9}],"symbol_id":"BINANCEFTS_PERP_XRP_USD...
2025-07-10 01:46:09,791 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:09.5740000Z', 'time_coinapi': '2025-07-09T23:46:09.7369917Z', 'asks': [], 'bids': [{'price': 2.406, 'size': 7879.9}], 'symbol_id': 'BINANCEFTS...
2025-07-10 01:46:09,791 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:10,138 INFO  📥 CoinAPI 5m message #26: {"type":"hearbeat"}...
2025-07-10 01:46:10,144 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:10,300 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:10,300 INFO  Performing final cleanup...
2025-07-10 01:46:10,346 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:10,349 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:10,349 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:10,349 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:10,349 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:10,414 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:10,417 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:10,424 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'spread': 56523, 'mid_price': 56523, 'tob_imbalance': 56523, 'depth_imbalance5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:10,440 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:10,451 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:10,451 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:10,451 INFO     Total features: 69
2025-07-10 01:46:10,451 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:10,451 INFO     DataFrame columns: 69
2025-07-10 01:46:10,470 INFO     close: 2.405400
2025-07-10 01:46:10,470 INFO     volume: 11.942529
2025-07-10 01:46:10,470 INFO     RSI_14: 35.795143
2025-07-10 01:46:10,471 INFO     EMA_21: 2.270887
2025-07-10 01:46:10,471 INFO     ATR_14: 0.000179
2025-07-10 01:46:10,471 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:10,471 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:10,471 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:10,472 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:10,472 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:10,472 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:10,525 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:10,526 INFO  🔧 State vector dimensions:
2025-07-10 01:46:10,526 INFO     Expected total obs: 1451
2025-07-10 01:46:10,526 INFO     Lookback: 30
2025-07-10 01:46:10,526 INFO     Features needed: 48
2025-07-10 01:46:10,526 INFO     Features from config: 69
2025-07-10 01:46:10,526 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:10,526 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:10,526 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:10,527 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:10,527 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:10,527 WARNING    Truncating 21 features to match model
2025-07-10 01:46:10,527 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:10,527 INFO  📥 CoinAPI 1s message #158: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:10.3560000Z","time_coinapi":"2025-07-09T23:46:10.5188318Z","asks":[{"price":2.4061,"size":10281.6}],"bids":[{"price":2.4060,"size":7913.7}],"symb...
2025-07-10 01:46:10,527 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:10.3560000Z', 'time_coinapi': '2025-07-09T23:46:10.5188318Z', 'asks': [{'price': 2.4061, 'size': 10281.6}], 'bids': [{'price': 2.406, 'size': ...
2025-07-10 01:46:10,527 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:10,529 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:10,530 INFO  🤖 Model decision: action=[-0.8425919  -0.02381533  0.40366483  0.95094144], price=$2.4059, pos=0
2025-07-10 01:46:10,530 INFO  🔍 Action variance (last 10): [0.42518997 0.32652852 0.31501487 0.31409577]
2025-07-10 01:46:10,530 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:10,530 INFO  🔍 Feature frame shape: (30, 48), last close: 2.405900
2025-07-10 01:46:10,531 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:10,531 INFO  🔍 State vector stats: mean=1.362960, std=1.881554, min=-0.258819, max=5.000000
2025-07-10 01:46:10,531 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:10,531 INFO  🔍 Normalized obs stats: mean=-2.367449, std=3.742414
2025-07-10 01:46:10,531 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4059]
2025-07-10 01:46:10,531 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:10,531 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:10,531 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:10,532 INFO  🔍 Feature variance - Close: 0.00000715, RSI: 0.00000000
2025-07-10 01:46:10,532 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:10,532 INFO  🔍 Current timestamp: 2025-07-09 23:46:10.472842+00:00
2025-07-10 01:46:10,532 INFO  🔍 1s mode: decision_ts=2025-07-09 23:46:02+00:00, current_ts=2025-07-09 23:46:10.472842+00:00
2025-07-10 01:46:10,532 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:46:02+00:00
2025-07-10 01:46:10,875 INFO  📥 CoinAPI 1s message #159: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:10.7130000Z","time_coinapi":"2025-07-09T23:46:10.8765414Z","asks":[{"price":2.4061,"size":8789.1},{"price":2.4062,"size":10626.4}],"bids":[{"pric...
2025-07-10 01:46:10,875 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:10.7130000Z', 'time_coinapi': '2025-07-09T23:46:10.8765414Z', 'asks': [{'price': 2.4061, 'size': 8789.1}, {'price': 2.4062, 'size': 10626.4}],...
2025-07-10 01:46:10,876 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:10,877 INFO  🎯 EVALUATION: 1.3s since last, processing at 2025-07-09 23:46:10.877808+00:00
2025-07-10 01:46:10,878 INFO  📊 Calculating features for decision...
2025-07-10 01:46:10,878 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:10,892 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:10,893 INFO  Calculating technical indicators...
2025-07-10 01:46:10,956 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:10,956 INFO  Calculating and merging order flow features...
2025-07-10 01:46:10,958 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:10,962 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:10,968 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:10,970 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:10,971 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:10,971 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:10,981 INFO  Calculating and merging order book features...
2025-07-10 01:46:10,984 INFO  Calculated OB features: 22
2025-07-10 01:46:10,985 WARNING Order book columns ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_bid_vol_l3', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:10,989 INFO  Merging order book features: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'depth_slope5']
2025-07-10 01:46:11,004 INFO  Successfully filled NaNs for merged OB columns: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'depth_slope5']
2025-07-10 01:46:11,005 INFO  Calculating time-since-event features...
2025-07-10 01:46:11,141 INFO  📥 CoinAPI 5m message #27: {"type":"hearbeat"}...
2025-07-10 01:46:11,147 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:11,196 INFO  📥 CoinAPI 1s message #160: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:10.7130000Z","time_coinapi":"2025-07-09T23:46:10.8765414Z","asks":[{"price":2.4078,"size":15179.2},{"price":2.4093,"size":4312.2}],"bids":[],"sym...
2025-07-10 01:46:11,206 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:10.7130000Z', 'time_coinapi': '2025-07-09T23:46:10.8765414Z', 'asks': [{'price': 2.4078, 'size': 15179.2}, {'price': 2.4093, 'size': 4312.2}],...
2025-07-10 01:46:11,211 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:11,555 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:11,556 INFO  Performing final cleanup...
2025-07-10 01:46:11,600 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:11,603 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:11,603 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:11,603 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:11,603 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:11,627 INFO  📥 CoinAPI 1s message #161: {"time_exchange":"2025-07-09T23:46:11.4040000Z","time_coinapi":"2025-07-09T23:46:11.5715546Z","uuid":"7057ea45-b20c-421e-8d66-c8e172f2f883","price":2.4061,"size":2.8,"taker_side":"BUY","symbol_id":"BI...
2025-07-10 01:46:11,627 INFO  📊 CoinAPI 1s parsed data: {'time_exchange': '2025-07-09T23:46:11.4040000Z', 'time_coinapi': '2025-07-09T23:46:11.5715546Z', 'uuid': '7057ea45-b20c-421e-8d66-c8e172f2f883', 'price': 2.4061, 'size': 2.8, 'taker_side': 'BUY', 'sy...
2025-07-10 01:46:11,627 INFO  📊 CoinAPI trade received for 1s: price=2.4061, size=2.8
2025-07-10 01:46:11,627 INFO  📊 1s OHLCV from trades: $2.406000 vol=23.00
2025-07-10 01:46:11,662 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:11,666 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:11,669 INFO  📥 CoinAPI 1s message #162: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:11.5050000Z","time_coinapi":"2025-07-09T23:46:11.6685666Z","asks":[{"price":2.4061,"size":8786.3},{"price":2.4063,"size":8812.4},{"price":2.4064,...
2025-07-10 01:46:11,669 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:11.5050000Z', 'time_coinapi': '2025-07-09T23:46:11.6685666Z', 'asks': [{'price': 2.4061, 'size': 8786.3}, {'price': 2.4063, 'size': 8812.4}, {...
2025-07-10 01:46:11,669 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:11,672 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'spread': 56523, 'mid_price': 56523, 'tob_imbalance': 56523, 'depth_imbalance5': 56523, 'depth_slope5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:11,686 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:11,697 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:11,697 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:11,697 INFO     Total features: 69
2025-07-10 01:46:11,697 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:11,697 INFO     DataFrame columns: 69
2025-07-10 01:46:11,714 INFO     close: 2.405400
2025-07-10 01:46:11,714 INFO     volume: 11.942529
2025-07-10 01:46:11,714 INFO     RSI_14: 35.795143
2025-07-10 01:46:11,714 INFO     EMA_21: 2.270887
2025-07-10 01:46:11,714 INFO     ATR_14: 0.000179
2025-07-10 01:46:11,714 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:11,715 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:11,715 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:11,716 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:11,716 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:11,716 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:11,867 INFO  📥 CoinAPI 1s message #163: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:11.6670000Z","time_coinapi":"2025-07-09T23:46:11.8301474Z","asks":[{"price":2.4065,"size":12083.3},{"price":2.4077,"size":12601.8},{"price":2.410...
2025-07-10 01:46:11,867 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:11.6670000Z', 'time_coinapi': '2025-07-09T23:46:11.8301474Z', 'asks': [{'price': 2.4065, 'size': 12083.3}, {'price': 2.4077, 'size': 12601.8},...
2025-07-10 01:46:11,867 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:11,868 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:11,869 INFO  🔧 State vector dimensions:
2025-07-10 01:46:11,869 INFO     Expected total obs: 1451
2025-07-10 01:46:11,869 INFO     Lookback: 30
2025-07-10 01:46:11,869 INFO     Features needed: 48
2025-07-10 01:46:11,869 INFO     Features from config: 69
2025-07-10 01:46:11,869 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:11,869 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:11,869 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:11,869 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:11,869 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:11,869 WARNING    Truncating 21 features to match model
2025-07-10 01:46:11,870 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:11,871 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:11,873 INFO  🤖 Model decision: action=[-0.57756054  0.6404402  -0.11738813  0.42176664], price=$2.4059, pos=0
2025-07-10 01:46:11,873 INFO  🔍 Action variance (last 10): [0.39687586 0.36735705 0.2959525  0.3178746 ]
2025-07-10 01:46:11,873 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:11,873 INFO  🔍 Feature frame shape: (30, 48), last close: 2.405900
2025-07-10 01:46:11,873 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:11,874 INFO  🔍 State vector stats: mean=1.362960, std=1.881554, min=-0.258819, max=5.000000
2025-07-10 01:46:11,874 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:11,874 INFO  🔍 Normalized obs stats: mean=-2.367449, std=3.742414
2025-07-10 01:46:11,874 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.4059]
2025-07-10 01:46:11,874 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:11,874 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:11,874 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:11,874 INFO  🔍 Feature variance - Close: 0.00000715, RSI: 0.00000000
2025-07-10 01:46:11,874 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:11,874 INFO  🔍 Current timestamp: 2025-07-09 23:46:11.716174+00:00
2025-07-10 01:46:11,875 INFO  🔍 1s mode: decision_ts=2025-07-09 23:46:02+00:00, current_ts=2025-07-09 23:46:11.716174+00:00
2025-07-10 01:46:11,875 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:46:02+00:00
2025-07-10 01:46:11,875 INFO  🎯 ENTRY CHECK: entry_sig=-0.350817, long_thr=0.7, short_thr=0.7, stable=True
2025-07-10 01:46:11,878 INFO  🎯 EVALUATION: 1.0s since last, processing at 2025-07-09 23:46:11.878066+00:00
2025-07-10 01:46:11,878 INFO  📊 Calculating features for decision...
2025-07-10 01:46:11,878 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:11,884 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:11,884 INFO  Calculating technical indicators...
2025-07-10 01:46:11,936 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:11,937 INFO  Calculating and merging order flow features...
2025-07-10 01:46:11,939 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:11,942 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:11,951 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:11,953 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:11,953 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:11,953 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:11,962 INFO  📥 CoinAPI 1s message #164: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:11.7900000Z","time_coinapi":"2025-07-09T23:46:11.9522393Z","asks":[{"price":2.4066,"size":9201.1},{"price":2.4067,"size":13590.9}],"bids":[],"sym...
2025-07-10 01:46:11,962 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:11.7900000Z', 'time_coinapi': '2025-07-09T23:46:11.9522393Z', 'asks': [{'price': 2.4066, 'size': 9201.1}, {'price': 2.4067, 'size': 13590.9}],...
2025-07-10 01:46:11,962 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:11,964 INFO  Calculating and merging order book features...
2025-07-10 01:46:11,968 INFO  Calculated OB features: 22
2025-07-10 01:46:11,969 WARNING Order book columns ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_ask_vol_l2', 'ob_ask_vol_l3'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:11,971 INFO  Merging order book features: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_ask_vol_l2', 'ob_ask_vol_l3']
2025-07-10 01:46:11,985 INFO  Successfully filled NaNs for merged OB columns: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_ask_vol_l2', 'ob_ask_vol_l3']
2025-07-10 01:46:11,985 INFO  Calculating time-since-event features...
2025-07-10 01:46:12,138 INFO  📥 CoinAPI 5m message #28: {"type":"hearbeat"}...
2025-07-10 01:46:12,144 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:12,222 INFO  📥 CoinAPI 1s message #165: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:12.0190000Z","time_coinapi":"2025-07-09T23:46:12.1845137Z","asks":[{"price":2.4098,"size":13330.9},{"price":2.4099,"size":69708.2},{"price":2.410...
2025-07-10 01:46:12,233 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:12.0190000Z', 'time_coinapi': '2025-07-09T23:46:12.1845137Z', 'asks': [{'price': 2.4098, 'size': 13330.9}, {'price': 2.4099, 'size': 69708.2},...
2025-07-10 01:46:12,246 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:12,396 INFO  📥 CoinAPI 1s message #166: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:12.1870000Z","time_coinapi":"2025-07-09T23:46:12.3505196Z","asks":[{"price":2.4066,"size":9214.0},{"price":2.4068,"size":10702.1},{"price":2.4781...
2025-07-10 01:46:12,403 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:12.1870000Z', 'time_coinapi': '2025-07-09T23:46:12.3505196Z', 'asks': [{'price': 2.4066, 'size': 9214.0}, {'price': 2.4068, 'size': 10702.1}, ...
2025-07-10 01:46:12,417 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:12,540 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:12,540 INFO  Performing final cleanup...
2025-07-10 01:46:12,584 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:12,586 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:12,586 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:12,586 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:12,586 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:12,652 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:12,656 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:12,661 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'spread': 56523, 'mid_price': 56523, 'tob_imbalance': 56523, 'depth_imbalance5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:12,677 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:12,688 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:12,689 INFO  📥 CoinAPI 1s message #167: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:12.5170000Z","time_coinapi":"2025-07-09T23:46:12.6799562Z","asks":[{"price":2.4370,"size":11.5},{"price":2.4399,"size":20797.5}],"bids":[{"price"...
2025-07-10 01:46:12,689 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:12,689 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:12.5170000Z', 'time_coinapi': '2025-07-09T23:46:12.6799562Z', 'asks': [{'price': 2.437, 'size': 11.5}, {'price': 2.4399, 'size': 20797.5}], 'b...
2025-07-10 01:46:12,689 INFO     Total features: 69
2025-07-10 01:46:12,689 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:12,689 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:12,689 INFO     DataFrame columns: 69
2025-07-10 01:46:12,709 INFO     close: 2.405400
2025-07-10 01:46:12,709 INFO     volume: 11.942529
2025-07-10 01:46:12,709 INFO     RSI_14: 35.795143
2025-07-10 01:46:12,709 INFO     EMA_21: 2.270887
2025-07-10 01:46:12,709 INFO     ATR_14: 0.000179
2025-07-10 01:46:12,709 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:12,709 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:12,709 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:12,710 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:12,710 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:12,710 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:12,761 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:12,762 INFO  🔧 State vector dimensions:
2025-07-10 01:46:12,762 INFO     Expected total obs: 1451
2025-07-10 01:46:12,762 INFO     Lookback: 30
2025-07-10 01:46:12,763 INFO     Features needed: 48
2025-07-10 01:46:12,763 INFO     Features from config: 69
2025-07-10 01:46:12,763 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:12,763 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:12,763 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:12,763 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:12,763 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:12,763 WARNING    Truncating 21 features to match model
2025-07-10 01:46:12,763 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:12,765 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:12,766 INFO  🤖 Model decision: action=[-0.9351654   0.28353214  0.80741215  0.2980746 ], price=$2.4060, pos=0
2025-07-10 01:46:12,766 INFO  🔍 Action variance (last 10): [0.47107738 0.3234817  0.31131762 0.2934809 ]
2025-07-10 01:46:12,767 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:12,767 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406000
2025-07-10 01:46:12,767 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:12,767 INFO  🔍 State vector stats: mean=1.362960, std=1.881554, min=-0.258819, max=5.000000
2025-07-10 01:46:12,767 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:12,767 INFO  🔍 Normalized obs stats: mean=-2.367450, std=3.742414
2025-07-10 01:46:12,767 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.406 ]
2025-07-10 01:46:12,768 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:12,768 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:12,768 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:12,768 INFO  🔍 Feature variance - Close: 0.00000699, RSI: 0.00000000
2025-07-10 01:46:12,768 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:12,768 INFO  🔍 Current timestamp: 2025-07-09 23:46:12.710627+00:00
2025-07-10 01:46:12,768 INFO  🔍 1s mode: decision_ts=2025-07-09 23:46:05+00:00, current_ts=2025-07-09 23:46:12.710627+00:00
2025-07-10 01:46:12,768 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:46:05+00:00
2025-07-10 01:46:13,114 INFO  📥 CoinAPI 5m message #29: {"type":"hearbeat"}...
2025-07-10 01:46:13,114 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:13,181 INFO  📥 CoinAPI 1s message #168: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:13.0060000Z","time_coinapi":"2025-07-09T23:46:13.1689139Z","asks":[{"price":2.4066,"size":9421.7},{"price":2.4067,"size":13383.2}],"bids":[{"pric...
2025-07-10 01:46:13,181 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:13.0060000Z', 'time_coinapi': '2025-07-09T23:46:13.1689139Z', 'asks': [{'price': 2.4066, 'size': 9421.7}, {'price': 2.4067, 'size': 13383.2}],...
2025-07-10 01:46:13,181 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:13,182 INFO  🎯 EVALUATION: 1.3s since last, processing at 2025-07-09 23:46:13.182839+00:00
2025-07-10 01:46:13,183 INFO  📊 Calculating features for decision...
2025-07-10 01:46:13,183 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:13,189 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:13,190 INFO  Calculating technical indicators...
2025-07-10 01:46:13,243 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:13,244 INFO  Calculating and merging order flow features...
2025-07-10 01:46:13,246 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:13,250 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:13,258 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:13,259 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:13,260 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:13,260 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:13,271 INFO  Calculating and merging order book features...
2025-07-10 01:46:13,274 INFO  Calculated OB features: 22
2025-07-10 01:46:13,275 WARNING Order book columns ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_bid_vol_l3', 'ob_bid_vol_l4', 'ob_bid_vol_l5', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:13,278 INFO  Merging order book features: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'depth_slope5']
2025-07-10 01:46:13,287 INFO  📥 CoinAPI 1s message #169: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:13.1300000Z","time_coinapi":"2025-07-09T23:46:13.2924621Z","asks":[],"bids":[{"price":1.9249,"size":8.1}],"symbol_id":"BINANCEFTS_PERP_XRP_USDC",...
2025-07-10 01:46:13,287 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:13.1300000Z', 'time_coinapi': '2025-07-09T23:46:13.2924621Z', 'asks': [], 'bids': [{'price': 1.9249, 'size': 8.1}], 'symbol_id': 'BINANCEFTS_P...
2025-07-10 01:46:13,287 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:13,297 INFO  Successfully filled NaNs for merged OB columns: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'depth_slope5']
2025-07-10 01:46:13,297 INFO  Calculating time-since-event features...
2025-07-10 01:46:13,572 INFO  📥 CoinAPI 1s message #170: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:13.3680000Z","time_coinapi":"2025-07-09T23:46:13.5303173Z","asks":[{"price":2.4078,"size":20370.6},{"price":2.4079,"size":11751.7}],"bids":[{"pri...
2025-07-10 01:46:13,579 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:13.3680000Z', 'time_coinapi': '2025-07-09T23:46:13.5303173Z', 'asks': [{'price': 2.4078, 'size': 20370.6}, {'price': 2.4079, 'size': 11751.7}]...
2025-07-10 01:46:13,582 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:13,696 INFO  📥 CoinAPI 1s message #171: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:13.4960000Z","time_coinapi":"2025-07-09T23:46:13.6585371Z","asks":[{"price":2.4078,"size":19591.9}],"bids":[{"price":2.4042,"size":3336.4},{"pric...
2025-07-10 01:46:13,708 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:13.4960000Z', 'time_coinapi': '2025-07-09T23:46:13.6585371Z', 'asks': [{'price': 2.4078, 'size': 19591.9}], 'bids': [{'price': 2.4042, 'size':...
2025-07-10 01:46:13,715 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:13,839 INFO  📥 CoinAPI 1s message #172: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:13.6360000Z","time_coinapi":"2025-07-09T23:46:13.7994077Z","asks":[],"bids":[{"price":1.9249,"size":8.1}],"symbol_id":"BINANCEFTS_PERP_XRP_USDC",...
2025-07-10 01:46:13,852 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:13.6360000Z', 'time_coinapi': '2025-07-09T23:46:13.7994077Z', 'asks': [], 'bids': [{'price': 1.9249, 'size': 8.1}], 'symbol_id': 'BINANCEFTS_P...
2025-07-10 01:46:13,858 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:13,862 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:13,862 INFO  Performing final cleanup...
2025-07-10 01:46:13,908 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:13,912 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:13,912 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:13,912 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:13,912 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:13,970 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:13,973 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:13,978 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'spread': 56523, 'mid_price': 56523, 'tob_imbalance': 56523, 'depth_imbalance5': 56523, 'depth_slope5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:13,991 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:14,001 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:14,001 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:14,001 INFO     Total features: 69
2025-07-10 01:46:14,001 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:14,001 INFO     DataFrame columns: 69
2025-07-10 01:46:14,020 INFO     close: 2.405400
2025-07-10 01:46:14,020 INFO     volume: 11.942529
2025-07-10 01:46:14,020 INFO     RSI_14: 35.795143
2025-07-10 01:46:14,020 INFO     EMA_21: 2.270887
2025-07-10 01:46:14,020 INFO     ATR_14: 0.000179
2025-07-10 01:46:14,021 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:14,021 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:14,021 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:14,022 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:14,022 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:14,022 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:14,070 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:14,071 INFO  🔧 State vector dimensions:
2025-07-10 01:46:14,071 INFO     Expected total obs: 1451
2025-07-10 01:46:14,071 INFO     Lookback: 30
2025-07-10 01:46:14,071 INFO     Features needed: 48
2025-07-10 01:46:14,071 INFO     Features from config: 69
2025-07-10 01:46:14,071 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:14,071 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:14,071 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:14,071 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:14,071 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:14,071 WARNING    Truncating 21 features to match model
2025-07-10 01:46:14,071 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:14,072 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:14,074 INFO  🤖 Model decision: action=[-0.83054876  0.15788984  0.85059905  0.20716858], price=$2.4060, pos=0
2025-07-10 01:46:14,074 INFO  🔍 Action variance (last 10): [0.44968128 0.30542287 0.33711353 0.29938516]
2025-07-10 01:46:14,074 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:14,074 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406000
2025-07-10 01:46:14,074 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:14,074 INFO  🔍 State vector stats: mean=1.362960, std=1.881554, min=-0.258819, max=5.000000
2025-07-10 01:46:14,074 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:14,074 INFO  🔍 Normalized obs stats: mean=-2.367450, std=3.742414
2025-07-10 01:46:14,075 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.406 ]
2025-07-10 01:46:14,075 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:14,075 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:14,075 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:14,075 INFO  🔍 Feature variance - Close: 0.00000699, RSI: 0.00000000
2025-07-10 01:46:14,075 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:14,075 INFO  🔍 Current timestamp: 2025-07-09 23:46:14.022407+00:00
2025-07-10 01:46:14,075 INFO  🔍 1s mode: decision_ts=2025-07-09 23:46:05+00:00, current_ts=2025-07-09 23:46:14.022407+00:00
2025-07-10 01:46:14,075 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:46:05+00:00
2025-07-10 01:46:14,115 INFO  📥 CoinAPI 5m message #30: {"type":"hearbeat"}...
2025-07-10 01:46:14,115 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:14,121 INFO  📥 CoinAPI 1s message #173: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:13.9370000Z","time_coinapi":"2025-07-09T23:46:14.0999575Z","asks":[{"price":2.4094,"size":18240.1},{"price":2.4095,"size":68883.6},{"price":2.409...
2025-07-10 01:46:14,121 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:13.9370000Z', 'time_coinapi': '2025-07-09T23:46:14.0999575Z', 'asks': [{'price': 2.4094, 'size': 18240.1}, {'price': 2.4095, 'size': 68883.6},...
2025-07-10 01:46:14,121 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:14,409 INFO  📥 CoinAPI 1s message #174: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:14.2220000Z","time_coinapi":"2025-07-09T23:46:14.3958307Z","asks":[],"bids":[{"price":2.3999,"size":5870.4},{"price":2.3997,"size":5242.4}],"symb...
2025-07-10 01:46:14,410 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:14.2220000Z', 'time_coinapi': '2025-07-09T23:46:14.3958307Z', 'asks': [], 'bids': [{'price': 2.3999, 'size': 5870.4}, {'price': 2.3997, 'size'...
2025-07-10 01:46:14,410 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:14,411 INFO  🎯 EVALUATION: 1.2s since last, processing at 2025-07-09 23:46:14.411217+00:00
2025-07-10 01:46:14,411 INFO  📊 Calculating features for decision...
2025-07-10 01:46:14,411 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:14,417 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:14,418 INFO  Calculating technical indicators...
2025-07-10 01:46:14,472 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:14,473 INFO  Calculating and merging order flow features...
2025-07-10 01:46:14,475 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:14,478 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:14,485 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:14,486 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:14,487 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:14,487 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:14,498 INFO  Calculating and merging order book features...
2025-07-10 01:46:14,501 INFO  Calculated OB features: 22
2025-07-10 01:46:14,502 WARNING Order book columns ['depth_imbalance5', 'ob_bid_vol_l1'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:14,504 INFO  Merging order book features: ['depth_imbalance5', 'ob_bid_vol_l1']
2025-07-10 01:46:14,515 INFO  Successfully filled NaNs for merged OB columns: ['depth_imbalance5', 'ob_bid_vol_l1']
2025-07-10 01:46:14,516 INFO  Calculating time-since-event features...
2025-07-10 01:46:14,561 INFO  📥 CoinAPI 1s message #175: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:14.3750000Z","time_coinapi":"2025-07-09T23:46:14.5384543Z","asks":[{"price":2.4078,"size":14400.5},{"price":2.4079,"size":16943.1},{"price":2.409...
2025-07-10 01:46:14,574 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:14.3750000Z', 'time_coinapi': '2025-07-09T23:46:14.5384543Z', 'asks': [{'price': 2.4078, 'size': 14400.5}, {'price': 2.4079, 'size': 16943.1},...
2025-07-10 01:46:14,586 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:14,679 INFO  📥 CoinAPI 1s message #176: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:14.4800000Z","time_coinapi":"2025-07-09T23:46:14.6430148Z","asks":[{"price":2.4093,"size":3112.6}],"bids":[{"price":2.4041,"size":25291.8}],"symb...
2025-07-10 01:46:14,679 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:14.4800000Z', 'time_coinapi': '2025-07-09T23:46:14.6430148Z', 'asks': [{'price': 2.4093, 'size': 3112.6}], 'bids': [{'price': 2.4041, 'size': ...
2025-07-10 01:46:14,679 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:14,954 INFO  📥 CoinAPI 1s message #177: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:14.7540000Z","time_coinapi":"2025-07-09T23:46:14.9162010Z","asks":[{"price":2.4095,"size":68070.5},{"price":2.4096,"size":29394.1}],"bids":[],"sy...
2025-07-10 01:46:14,959 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:14.7540000Z', 'time_coinapi': '2025-07-09T23:46:14.9162010Z', 'asks': [{'price': 2.4095, 'size': 68070.5}, {'price': 2.4096, 'size': 29394.1}]...
2025-07-10 01:46:14,966 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:15,072 INFO  Successfully calculated time-since-event features
2025-07-10 01:46:15,072 INFO  Performing final cleanup...
2025-07-10 01:46:15,118 INFO  📥 CoinAPI 5m message #31: {"type":"hearbeat"}...
2025-07-10 01:46:15,119 INFO  📊 CoinAPI 5m parsed data: {'type': 'hearbeat'}...
2025-07-10 01:46:15,128 INFO  Final DataFrame shape: (56523, 69). Columns: ['open', 'high', 'low', 'close', 'volume', 'buy_volume', 'sell_volume', 'trade_count', 'vwap', 'ATR_14', 'RSI_14', 'EMA_9', 'EMA_21', 'ADX_14', 'DMP_14', 'DMN_14', 'volume_delta_30s', 'volume_delta_2m', 'trade_count_delta_100t', 'trade_count_delta_500t', 'cvd_reset_daily', 'hmm_state_3c_volatility_5m', 'VWAP_pta', 'bollinger_bands_upper_20_2.0', 'bollinger_bands_middle_20_2.0', 'bollinger_bands_lower_20_2.0', 'bollinger_bands_width_20_2.0', 'spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'depth_slope5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_ask_vol_l3', 'ob_bid_vol_l4', 'ob_ask_vol_l4', 'ob_bid_vol_l5', 'ob_ask_vol_l5', 'dvol_bid_l1', 'dvol_ask_l1', 'trade_dir_sum_1s', 'trade_skew_1s', 'dt_since_buy', 'dt_since_sell', 'hmm_state_3c_volatility_1s', 'volume_imbalance_1s', 'ATR_14_5m', 'VWAP_pta_5m', 'bollinger_bands_lower_20_2.0_5m', 'bollinger_bands_middle_20_2.0_5m', 'bollinger_bands_upper_20_2.0_5m', 'bollinger_bands_width_20_2.0_5m', 'ADX_14_5m', 'DMP_14_5m', 'DMN_14_5m', 'RSI_14_5m', 'EMA_9_5m', 'EMA_21_5m', 'volume_imbalance_5m', 'last_trade_dt', 'ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:15,131 INFO  📊 Features calculated by indicators: 69 features
2025-07-10 01:46:15,131 INFO  📊 Base DataFrame columns: 69 columns
2025-07-10 01:46:15,131 INFO  📊 Processing features for model compatibility...
2025-07-10 01:46:15,131 INFO  🔍 DEBUG: feature_cols from config: 69 features
2025-07-10 01:46:15,183 INFO  📥 CoinAPI 1s message #178: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:14.9990000Z","time_coinapi":"2025-07-09T23:46:15.1616648Z","asks":[{"price":2.4078,"size":15054.2},{"price":2.4093,"size":4388.3},{"price":2.4095...
2025-07-10 01:46:15,183 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:14.9990000Z', 'time_coinapi': '2025-07-09T23:46:15.1616648Z', 'asks': [{'price': 2.4078, 'size': 15054.2}, {'price': 2.4093, 'size': 4388.3}, ...
2025-07-10 01:46:15,183 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:15,189 INFO  🔍 DEBUG: feature_cols after feature smoothing: 69 features
2025-07-10 01:46:15,191 INFO  ✅ Filtered by essential features: 56523 rows remaining
2025-07-10 01:46:15,196 INFO  📊 Filling NaN in non-essential features: {'ATR_14': 14, 'RSI_14': 14, 'EMA_9': 8, 'EMA_21': 20, 'ADX_14': 27, 'DMP_14': 14, 'DMN_14': 14, 'cvd_reset_daily': 56523, 'bollinger_bands_upper_20_2.0': 19, 'bollinger_bands_middle_20_2.0': 19, 'bollinger_bands_lower_20_2.0': 19, 'bollinger_bands_width_20_2.0': 19, 'depth_imbalance5': 56523, 'trade_dir_sum_1s': 56523, 'ATR_14_5m': 14, 'VWAP_pta_5m': 56523, 'bollinger_bands_lower_20_2.0_5m': 19, 'bollinger_bands_middle_20_2.0_5m': 19, 'bollinger_bands_upper_20_2.0_5m': 19, 'bollinger_bands_width_20_2.0_5m': 19, 'ADX_14_5m': 27, 'DMP_14_5m': 14, 'DMN_14_5m': 14, 'RSI_14_5m': 14, 'EMA_9_5m': 8, 'EMA_21_5m': 20, 'last_trade_dt': 56523, 'ob_price_off_l1': 56523, 'ob_price_off_l2': 56523, 'ob_price_off_l3': 56523, 'ob_price_off_l4': 56523, 'ob_price_off_l5': 56523}
2025-07-10 01:46:15,217 INFO  🔍 DEBUG: feature_cols before base_df selection: 69 features
2025-07-10 01:46:15,236 INFO  🔍 DEBUG: feature_cols after base_df selection: 69 features
2025-07-10 01:46:15,236 INFO  📊 Feature processing diagnostics:
2025-07-10 01:46:15,237 INFO     Total features: 69
2025-07-10 01:46:15,237 INFO     DataFrame shape: (56523, 69)
2025-07-10 01:46:15,237 INFO     DataFrame columns: 69
2025-07-10 01:46:15,261 INFO     close: 2.405400
2025-07-10 01:46:15,262 INFO     volume: 11.942529
2025-07-10 01:46:15,262 INFO     RSI_14: 35.795143
2025-07-10 01:46:15,262 INFO     EMA_21: 2.270887
2025-07-10 01:46:15,262 INFO     ATR_14: 0.000179
2025-07-10 01:46:15,262 INFO  ✅ Feature processing complete: 69 features, 56523 rows
2025-07-10 01:46:15,262 WARNING ⚠️ INDICATORS UNCHANGED: Hash 7225446112128484232
2025-07-10 01:46:15,262 WARNING    This may indicate stale 5m data or calculation issues
2025-07-10 01:46:15,263 WARNING ⚠️ LOW VARIANCE in RSI_14: 0.0000000000
2025-07-10 01:46:15,263 WARNING ⚠️ LOW VARIANCE in EMA_21: 0.0000000000
2025-07-10 01:46:15,264 WARNING ⚠️ LOW VARIANCE in ATR_14: 0.0000000000
2025-07-10 01:46:15,312 INFO  📥 CoinAPI 1s message #179: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:15.1150000Z","time_coinapi":"2025-07-09T23:46:15.2784284Z","asks":[{"price":2.4068,"size":10707.5},{"price":2.4121,"size":3886.7},{"price":2.4122...
2025-07-10 01:46:15,312 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:15.1150000Z', 'time_coinapi': '2025-07-09T23:46:15.2784284Z', 'asks': [{'price': 2.4068, 'size': 10707.5}, {'price': 2.4121, 'size': 3886.7}, ...
2025-07-10 01:46:15,312 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:15,313 INFO  ✅ Forward-fill complete: 69 features, 56523 rows
2025-07-10 01:46:15,314 INFO  🔧 State vector dimensions:
2025-07-10 01:46:15,315 INFO     Expected total obs: 1451
2025-07-10 01:46:15,315 INFO     Lookback: 30
2025-07-10 01:46:15,315 INFO     Features needed: 48
2025-07-10 01:46:15,315 INFO     Features from config: 69
2025-07-10 01:46:15,315 INFO     Meta features: 11 (7 trade + 4 time)
2025-07-10 01:46:15,315 INFO  🔍 DEBUG: feature_cols before dimension check: 69 features
2025-07-10 01:46:15,315 WARNING 🔍 DEBUG: feature_cols seems too long! First 5: ['open', 'high', 'low', 'close', 'volume'], Last 5: ['ob_price_off_l1', 'ob_price_off_l2', 'ob_price_off_l3', 'ob_price_off_l4', 'ob_price_off_l5']
2025-07-10 01:46:15,315 WARNING ⚠️ FEATURE DIMENSION MISMATCH:
2025-07-10 01:46:15,315 WARNING    Model expects 48 features but config has 69
2025-07-10 01:46:15,315 WARNING    Truncating 21 features to match model
2025-07-10 01:46:15,315 INFO     Using first 48 features: ['open', 'high', 'low']...['trade_skew_1s', 'dt_since_buy', 'dt_since_sell']
2025-07-10 01:46:15,317 INFO  📊 Final feature selection: 48 features
2025-07-10 01:46:15,319 INFO  🤖 Model decision: action=[0.11650491 0.1732378  0.4532584  0.9337034 ], price=$2.4060, pos=0
2025-07-10 01:46:15,319 INFO  🔍 Action variance (last 10): [0.41464964 0.24215825 0.31085426 0.35749632]
2025-07-10 01:46:15,319 INFO  🔍 State vector length: 1451, shape: (1451,)
2025-07-10 01:46:15,319 INFO  🔍 Feature frame shape: (30, 48), last close: 2.406000
2025-07-10 01:46:15,319 INFO  🔍 Trade features: pos=0, pnl_norm=0.000000
2025-07-10 01:46:15,319 INFO  🔍 State vector stats: mean=1.409903, std=1.875274, min=-0.258819, max=5.000000
2025-07-10 01:46:15,319 INFO  🔍 VecNormalize: training=False, norm_obs=True
2025-07-10 01:46:15,319 INFO  🔍 Normalized obs stats: mean=-2.228781, std=3.674744
2025-07-10 01:46:15,320 INFO  🔍 Last 5 closes: [2.4133 2.4123 2.4089 2.4087 2.406 ]
2025-07-10 01:46:15,320 INFO  🔍 Last 5 RSI values: [35.795143 35.795143 35.795143 35.795143 35.795143]
2025-07-10 01:46:15,320 INFO  🔍 Last 5 EMA values: [2.2708871 2.2708871 2.2708871 2.2708871 2.2708871]
2025-07-10 01:46:15,321 INFO  🔍 Last 5 ATR values: [0.00017854 0.00017854 0.00017854 0.00017854 0.00017854]
2025-07-10 01:46:15,321 INFO  🔍 Feature variance - Close: 0.00000699, RSI: 0.00000000
2025-07-10 01:46:15,321 INFO  🔍 Feature variance - EMA: 0.00000000, ATR: 0.00000000
2025-07-10 01:46:15,321 INFO  🔍 Current timestamp: 2025-07-09 23:46:15.264104+00:00
2025-07-10 01:46:15,321 INFO  🔍 1s mode: decision_ts=2025-07-09 23:46:05+00:00, current_ts=2025-07-09 23:46:15.264104+00:00
2025-07-10 01:46:15,321 INFO  🔍 Latest 1s data timestamp: 2025-07-09 23:46:05+00:00
2025-07-10 01:46:15,391 INFO  📥 CoinAPI 1s message #180: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:15.2220000Z","time_coinapi":"2025-07-09T23:46:15.3845491Z","asks":[],"bids":[{"price":2.4038,"size":7593.8}],"symbol_id":"BINANCEFTS_PERP_XRP_USD...
2025-07-10 01:46:15,391 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:15.2220000Z', 'time_coinapi': '2025-07-09T23:46:15.3845491Z', 'asks': [], 'bids': [{'price': 2.4038, 'size': 7593.8}], 'symbol_id': 'BINANCEFT...
2025-07-10 01:46:15,391 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:15,551 INFO  📥 CoinAPI 1s message #181: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:15.3810000Z","time_coinapi":"2025-07-09T23:46:15.5443605Z","asks":[{"price":2.4061,"size":10278.8},{"price":2.4062,"size":9133.9},{"price":2.4078...
2025-07-10 01:46:15,551 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:15.3810000Z', 'time_coinapi': '2025-07-09T23:46:15.5443605Z', 'asks': [{'price': 2.4061, 'size': 10278.8}, {'price': 2.4062, 'size': 9133.9}, ...
2025-07-10 01:46:15,551 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:15,553 INFO  🎯 EVALUATION: 1.1s since last, processing at 2025-07-09 23:46:15.553186+00:00
2025-07-10 01:46:15,553 INFO  📥 CoinAPI 1s message #182: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:15.3810000Z","time_coinapi":"2025-07-09T23:46:15.5443605Z","asks":[{"price":2.4079,"size":11751.7}],"bids":[],"symbol_id":"BINANCEFTS_PERP_XRP_US...
2025-07-10 01:46:15,553 INFO  📊 Calculating features for decision...
2025-07-10 01:46:15,553 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:15.3810000Z', 'time_coinapi': '2025-07-09T23:46:15.5443605Z', 'asks': [{'price': 2.4079, 'size': 11751.7}], 'bids': [], 'symbol_id': 'BINANCEF...
2025-07-10 01:46:15,554 INFO  🔧 Using timeframe '5m' for indicator calculations (primary: 1s)
2025-07-10 01:46:15,554 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:15,561 INFO  Checking and fixing data quality for indicator timeframe...
2025-07-10 01:46:15,562 INFO  Calculating technical indicators...
2025-07-10 01:46:15,622 INFO  Calculating Volume Imbalance...
2025-07-10 01:46:15,623 INFO  Calculating and merging order flow features...
2025-07-10 01:46:15,624 WARNING Order flow columns ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:15,628 INFO  Merging order flow features: ['volume_delta_30s', 'volume_delta_2m', 'cvd_reset_daily']
2025-07-10 01:46:15,635 INFO  Calculating and merging High-Frequency trade features...
2025-07-10 01:46:15,637 INFO  Calculated HF Trade features: 3
2025-07-10 01:46:15,637 INFO  Merging HF Trade features: ['last_trade_dt', 'trade_dir_sum_1s']
2025-07-10 01:46:15,637 WARNING HF Trade columns ['trade_dir_sum_1s'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:15,646 INFO  📥 CoinAPI 1s message #183: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:15.4850000Z","time_coinapi":"2025-07-09T23:46:15.6479197Z","asks":[{"price":2.4078,"size":14400.5},{"price":2.4079,"size":16943.1}],"bids":[],"sy...
2025-07-10 01:46:15,647 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:15.4850000Z', 'time_coinapi': '2025-07-09T23:46:15.6479197Z', 'asks': [{'price': 2.4078, 'size': 14400.5}, {'price': 2.4079, 'size': 16943.1}]...
2025-07-10 01:46:15,647 INFO  📊 CoinAPI orderbook received for 1s
2025-07-10 01:46:15,648 INFO  Calculating and merging order book features...
2025-07-10 01:46:15,652 INFO  Calculated OB features: 22
2025-07-10 01:46:15,653 WARNING Order book columns ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_bid_vol_l3', 'ob_bid_vol_l4', 'ob_bid_vol_l5', 'depth_slope5'] exist in base DataFrame. Dropping them before merge.
2025-07-10 01:46:15,655 INFO  Merging order book features: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'depth_slope5']
2025-07-10 01:46:15,675 INFO  Successfully filled NaNs for merged OB columns: ['spread', 'mid_price', 'tob_imbalance', 'depth_imbalance5', 'ob_price_off_l1', 'ob_bid_vol_l1', 'ob_ask_vol_l1', 'ob_price_off_l2', 'ob_bid_vol_l2', 'ob_ask_vol_l2', 'ob_price_off_l3', 'ob_bid_vol_l3', 'ob_price_off_l4', 'ob_bid_vol_l4', 'ob_price_off_l5', 'ob_bid_vol_l5', 'depth_slope5']
2025-07-10 01:46:15,675 INFO  Calculating time-since-event features...
2025-07-10 01:46:15,809 INFO  📥 CoinAPI 1s message #184: {"is_snapshot":false,"time_exchange":"2025-07-09T23:46:15.6250000Z","time_coinapi":"2025-07-09T23:46:15.7880262Z","asks":[{"price":2.4129,"size":20064.3},{"price":2.4130,"size":101667.6}],"bids":[{"pr...
2025-07-10 01:46:15,816 INFO  📊 CoinAPI 1s parsed data: {'is_snapshot': False, 'time_exchange': '2025-07-09T23:46:15.6250000Z', 'time_coinapi': '2025-07-09T23:46:15.7880262Z', 'asks': [{'price': 2.4129, 'size': 20064.3}, {'price': 2.413, 'size': 101667.6}]...
2025-07-10 01:46:15,833 INFO  📊 CoinAPI orderbook received for 1s
