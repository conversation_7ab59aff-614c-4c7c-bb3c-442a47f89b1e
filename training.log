2025-07-10 13:56:23,262 INFO  Loading config from: strategyConfig_scalp_1s.json
2025-07-10 13:56:23,262 INFO  🔍 DEBUG: Using CoinAPI key: a49bb33b...1cef
2025-07-10 13:56:23,262 INFO  Config loaded for symbol: XRPUSDC
2025-07-10 13:56:23,262 INFO  🚀 Starting live trading simulation
2025-07-10 13:56:23,262 INFO  📊 Symbol: XRPUSDC
2025-07-10 13:56:23,262 INFO  📊 Timeframe: 1s
2025-07-10 13:56:23,262 INFO  📊 Date range: 2025-07-05 to 2025-07-05
2025-07-10 13:56:23,262 INFO  Loading historical data for XRPUSDC from 2025-07-05 to 2025-07-05
2025-07-10 13:56:23,264 INFO  Loading: parquet_processed/XRPUSDC/1s/2025-07-05.parquet
2025-07-10 13:56:23,298 INFO  Loaded 14004 rows from 2025-07-05 00:00:04+00:00 to 2025-07-05 23:59:51+00:00
2025-07-10 13:56:23,298 INFO  🚀 Starting live trading simulation...
2025-07-10 13:56:23,298 INFO  Symbol: XRPUSDC
2025-07-10 13:56:23,298 INFO  Timeframe: 1s
2025-07-10 13:56:23,299 INFO  Use 1s decisions: True
2025-07-10 13:56:23,299 INFO  Using model: sac_9996800_steps.zip
2025-07-10 13:56:23,299 INFO  Using vecnorm: sac_9996800.vecnorm.pkl
2025-07-10 13:56:23,299 INFO  🔧 Environment setup:
2025-07-10 13:56:23,299 INFO     Feature columns: 48 features
2025-07-10 13:56:23,299 INFO     Lookback: 30
2025-07-10 13:56:23,299 INFO     Expected obs size: 1451
2025-07-10 13:56:23,872 ERROR ❌ Simulation failed: The environment is of type <class 'NoneType'>, not a Gymnasium environment. In this case, we expect OpenAI Gym to be installed and the environment to be an OpenAI Gym environment.
2025-07-10 13:56:23,874 ERROR Traceback (most recent call last):
  File "/Users/<USER>/Projects/scalpel_new/simulate_live_trading.py", line 577, in main
    results = simulate_live_trading_logic(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Projects/scalpel_new/simulate_live_trading.py", line 252, in simulate_live_trading_logic
    vecnorm = VecNormalize.load(latest_vecnorm, DummyVecEnv([lambda: None]))
                                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/dummy_vec_env.py", line 31, in __init__
    self.envs = [_patch_env(fn()) for fn in env_fns]
                 ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/patch_gym.py", line 33, in _patch_env
    raise ValueError(
ValueError: The environment is of type <class 'NoneType'>, not a Gymnasium environment. In this case, we expect OpenAI Gym to be installed and the environment to be an OpenAI Gym environment.

