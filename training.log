2025-07-10 14:07:33,520 INFO  Loading config from: strategyConfig_scalp_1s.json
2025-07-10 14:07:33,520 INFO  🔍 DEBUG: Using CoinAPI key: a49bb33b...1cef
2025-07-10 14:07:33,521 INFO  Config loaded for symbol: XRPUSDC
2025-07-10 14:07:33,521 INFO  🚀 Starting live trading simulation
2025-07-10 14:07:33,521 INFO  📊 Symbol: XRPUSDC
2025-07-10 14:07:33,521 INFO  📊 Timeframe: 1s
2025-07-10 14:07:33,521 INFO  📊 Date range: 2025-07-05 to 2025-07-05
2025-07-10 14:07:33,521 INFO  Loading historical data for XRPUSDC from 2025-07-05 to 2025-07-05
2025-07-10 14:07:33,522 INFO  Loading: parquet_processed/XRPUSDC/1s/2025-07-05.parquet
2025-07-10 14:07:33,559 INFO  Loaded 14004 rows from 2025-07-05 00:00:04+00:00 to 2025-07-05 23:59:51+00:00
2025-07-10 14:07:33,559 INFO  🚀 Starting live trading simulation...
2025-07-10 14:07:33,559 INFO  Symbol: XRPUSDC
2025-07-10 14:07:33,559 INFO  Timeframe: 1s
2025-07-10 14:07:33,559 INFO  Use 1s decisions: True
2025-07-10 14:07:33,559 INFO  Using model: sac_9996800_steps.zip
2025-07-10 14:07:33,560 INFO  Using vecnorm: sac_9996800.vecnorm.pkl
2025-07-10 14:07:33,560 INFO  🔧 Environment setup:
2025-07-10 14:07:33,560 INFO     Feature columns: 48 features
2025-07-10 14:07:33,560 INFO     Lookback: 30
2025-07-10 14:07:33,560 INFO     Expected obs size: 1451
2025-07-10 14:07:34,292 ERROR ❌ Simulation failed: maximum recursion depth exceeded
2025-07-10 14:07:34,306 ERROR Traceback (most recent call last):
  File "/Users/<USER>/Projects/scalpel_new/simulate_live_trading.py", line 712, in main
    results = simulate_live_trading_logic(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Projects/scalpel_new/simulate_live_trading.py", line 259, in simulate_live_trading_logic
    obs_rms = vecnorm_data.get('obs_rms', None)
              ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 473, in getattr_depth_check
    all_attributes = self._get_all_attributes()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 447, in _get_all_attributes
    all_attributes.update(self.class_attributes)
                          ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/.pyenv/versions/3.12.3/lib/python3.12/site-packages/stable_baselines3/common/vec_env/base_vec_env.py", line 430, in __getattr__
    blocked_class = self.getattr_depth_check(name, already_found=False)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RecursionError: maximum recursion depth exceeded

